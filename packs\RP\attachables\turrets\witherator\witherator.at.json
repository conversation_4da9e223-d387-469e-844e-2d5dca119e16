{"format_version": "1.13.0", "minecraft:attachable": {"description": {"identifier": "rza:witherator_item", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/entity/turrets/witherator/witherator"}, "geometry": {"default": "geometry.witherator_item"}, "animations": {"first_person_hold": "animation.witherator.first_person_hold", "third_person_hold": "animation.witherator.third_person_hold", "general": "controller.animation.attachable.general"}, "scripts": {"animate": ["general"]}, "render_controllers": ["controller.render.model_default"]}}}