import { Entity, Player, system } from "@minecraft/server";
import { ModalFormData } from "@minecraft/server-ui";

/**
 * Manages turret configuration UI and settings based on turret type.
 * Serves as the main entry point for turret configuration handling.
 *
 * @param entity - The turret entity to be configured
 *
 * @remarks
 * Supports different turret types:
 * - Arrow Turret
 * - Pyro Charger
 * - Sonic Cannon
 * - Storm Weaver
 * - Witherator
 * - Laser Turret
 * - Pulsar System (handled separately)
 */
export function handleTurretConfiguration(entity: Entity) {
  const turret = entity;
  const turretType = turret.typeId;
  const configurator = turret.dimension.getPlayers({
    closest: 1,
    location: turret.location,
  });
  const player = configurator[0];

  if (!player) return;

  const turretConfigs: Record<string, string> = {
    "rza:arrow_turret": "§2Arrow Turret§r",
    "rza:pyro_charger": "§6Pyro Charger§r",
    "rza:sonic_cannon": "§5Sonic Cannon§r",
    "rza:storm_weaver": "§cStorm Weaver§r",
    "rza:witherator": "§6Witherator§r",
    "rza:laser_turret": "§6Laser Turret§r",
  };

  if (turretType === "rza:pulsar_system") {
    let confPulsarSystem = system.run(() => {
      pulsarSystemConfigurator(player, turret);
      system.clearRun(confPulsarSystem);
    });
    return;
  }

  const turretConfig = turretConfigs[turretType];
  if (turretConfig) {
    let configRun = system.run(() => {
      turretConfigurator(player, turret, turretConfig);
      system.clearRun(configRun);
    });
  }
}

/**
 * Configures standard turret targeting settings through a UI form.
 *
 * @param player - The player configuring the turret
 * @param turret - The turret being configured
 * @param turretName - Display name of the turret (includes color formatting)
 *
 * @remarks
 * Configuration options:
 * - Mutant prioritization (toggle)
 * - Target selection (dropdown):
 *   - All zombies
 *   - Specific types (Walkers, Miners, etc.)
 *
 * Saved Properties:
 * - rza:prioritize_mutants (boolean)
 * - rza:target_zombies (string)
 */
export function turretConfigurator(
  player: Player,
  turret: Entity,
  turretName: string,
) {
  // Retrieve existing configuration from turret properties
  let zombieSelection = ["Walkers", "Miners", "Ferals", "Spitters", "Alphas"];
  const selectedZombieType = turret.getProperty("rza:target_zombies") as string;

  // Reorder selection array to show current selection first
  // Remove the selectedZombieType from the array
  zombieSelection = zombieSelection.filter(
    (zombieType) => zombieType !== selectedZombieType,
  );

  // Add the selectedZombieType as the first element in the array
  zombieSelection.unshift(selectedZombieType);

  new ModalFormData()
    // Form setup with current configuration values
    .title(`${turretName}`)
    .toggle("Prioritize Mutated Zombies", {
      defaultValue: turret.getProperty("rza:prioritize_mutants") as boolean,
      tooltip: "Toggle prioritizing mutated zombies",
    })
    .dropdown("§cPriority Target", zombieSelection)
    .show(player)
    .then((result) => {
      if (!result || !result.formValues) return;

      // Extract form values
      const [toggle, dropdown] = result.formValues;
      const selectedZombieType = zombieSelection[dropdown as number];

      // Update turret properties with new configuration
      if (toggle !== undefined) {
        turret.setProperty("rza:prioritize_mutants", toggle);
      }
      if (selectedZombieType !== undefined) {
        turret.setProperty("rza:target_zombies", selectedZombieType);
      }

      // Handle different targeting combinations
      // Each combination updates messages and triggers appropriate events
      //All Zombies: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "All") {
        player.sendMessage(
          `[${turretName}] Targeting §cAll Zombies§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_all_zombies");
      }

      //All Zombies: Prioritize Mutants
      if (toggle && selectedZombieType === "All") {
        player.sendMessage(
          `[${turretName}] Targeting §cAll Zombies§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_all_zombies_prioritize_mutants");
      }

      //Walkers: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "Walkers") {
        player.sendMessage(
          `[${turretName}] Targeting §cWalkers§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_walkers");
      }

      //Walkers: Prioritize Mutants
      if (toggle && selectedZombieType === "Walkers") {
        player.sendMessage(
          `[${turretName}] Targeting §cWalkers§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_walkers_prioritize_mutants");
      }

      //Miners: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "Miners") {
        player.sendMessage(
          `[${turretName}] Targeting §cMiners§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_miners");
      }

      //Miners: Prioritize Mutants
      if (toggle && selectedZombieType === "Miners") {
        player.sendMessage(
          `[${turretName}] Targeting §cMiners§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_miners_prioritize_mutants");
      }

      //Ferals: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "Ferals") {
        player.sendMessage(
          `[${turretName}] Targeting §cFerals§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_ferals");
      }

      //Ferals: Prioritize Mutants
      if (toggle && selectedZombieType === "Ferals") {
        player.sendMessage(
          `[${turretName}] Targeting §cFerals§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_ferals_prioritize_mutants");
      }

      //Spitters: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "Spitters") {
        player.sendMessage(
          `[${turretName}] Targeting §cSpitters§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_spitters");
      }

      //Spitters: Prioritize Mutants
      if (toggle && selectedZombieType === "Spitters") {
        player.sendMessage(
          `[${turretName}] Targeting §cSpitters§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_spitters_prioritize_mutants");
      }

      //Alphas: Don't Prioritize Mutants
      if (!toggle && selectedZombieType === "Alphas") {
        player.sendMessage(
          `[${turretName}] Targeting §cAlphas§r: Not Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_alphas");
      }

      //Alphas: Prioritize Mutants
      if (toggle && selectedZombieType === "Alphas") {
        player.sendMessage(
          `[${turretName}] Targeting §cAlphas§r: Prioritizing Mutants`,
        );
        turret.triggerEvent("rza:target_alphas_prioritize_mutants");
      }
    })
    .catch(() => {
      // Error handling for failed configuration
      player.sendMessage(
        `[SYSTEM] §cConfiguration Canceled§r: Resetting to previous configuration.`,
      );
    });
  return;
}

/**
 * Specialized configuration handler for Pulsar System turrets.
 *
 * @param player - The player configuring the Pulsar System
 * @param turret - The Pulsar System turret entity
 *
 * @remarks
 * Configuration options:
 * - System active state (toggle)
 * - Conversion type (Charcoal/XP)
 *
 * Saved Properties:
 * - rza:active_state (boolean)
 * - rza:convert_items_to (string)
 *
 * @example
 * ```ts
 * // Manual configuration trigger
 * pulsarSystemConfigurator(player, pulsarTurret);
 * ```
 */
function pulsarSystemConfigurator(player: Player, turret: Entity) {
  // Retrieve existing configuration from turret properties
  let convertTo = ["Charcoal", "XP"];
  const selectedConvertType = turret.getProperty(
    "rza:convert_items_to",
  ) as string;

  // Reorder selection array to show current selection first
  // Remove the selectedConvertType from the array
  convertTo = convertTo.filter(
    (convertType) => convertType !== selectedConvertType,
  );

  // Add the selectedConvertType as the first element in the array
  convertTo.unshift(selectedConvertType);

  new ModalFormData()
    // Form setup with current configuration values
    .title("§cPulsar System§r")
    .toggle("Active", {
      defaultValue: turret.getProperty("rza:active_state") as boolean,
      tooltip: "Toggle the Pulsar System on or off",
    })
    .dropdown("Convert Items to", convertTo)
    .show(player)
    .then((result) => {
      if (!result || !result.formValues) return;

      // Extract form values
      const [toggle, dropdown] = result.formValues;
      const selectedConvertType = convertTo[dropdown as number];

      // Handle different conversion combinations
      // Each combination updates messages and triggers appropriate events
      //Active: Convert Items to Charcoal
      if (toggle && selectedConvertType === "Charcoal") {
        player.sendMessage(
          "[§cPulsar System§r] §2Active§r: Converting items to Charcoal",
        );
      }

      //Active: Convert Items to XP Orbs
      if (toggle && selectedConvertType === "XP") {
        player.sendMessage(
          "[§cPulsar System§r] §2Active§r: Converting items to XP Orbs",
        );
      }

      //Inactive: Convert Items to Charcoal
      if (!toggle && selectedConvertType === "Charcoal") {
        player.sendMessage(
          "[§cPulsar System§r] §4Inactive§r: Converting items to Charcoal",
        );
      }

      //Inactive: Convert Items to XP Orbs
      if (!toggle && selectedConvertType === "XP") {
        player.sendMessage(
          "[§cPulsar System§r] §4Inactive§r: Converting items to XP Orbs",
        );
      }

      // Update turret properties with new configuration
      if (toggle !== undefined) {
        turret.setProperty("rza:active_state", toggle);
      }
      if (selectedConvertType !== undefined) {
        turret.setProperty("rza:convert_items_to", selectedConvertType);
      }
    })
    .catch(() => {
      // Error handling for failed configuration
      player.sendMessage(
        `[SYSTEM] §cConfiguration Canceled§r: Resetting to previous configuration.`,
      );
    });
  return;
}
