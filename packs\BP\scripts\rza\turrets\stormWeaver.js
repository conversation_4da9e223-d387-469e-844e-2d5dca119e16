import { EntityDamageCause, system } from "@minecraft/server";
import { calculateDistance, fixedPosRaycast, fixedLenRaycast } from "./raycast";
import { calculateDirection } from "../utils/vector3";
export let stormWeavers = {
    "rza:chain_length": new Map(),
    "rza:chained_zombies": new Map(),
    "rza:destroyed_weavers": new Set(),
};
export function stormWeaverLightning(chainer, stormWeaver) {
    const stormWeaverId = stormWeaver.id;
    if (!stormWeavers["rza:chained_zombies"].has(stormWeaverId)) {
        stormWeavers["rza:chained_zombies"].set(stormWeaverId, []);
    }
    const chainedZombies = stormWeavers["rza:chained_zombies"].get(stormWeaverId);
    const chainLength = stormWeavers["rza:chain_length"].get(stormWeaverId);
    if (chainLength && chainLength > 0) {
        try {
            if (chainer) {
                chainer.applyDamage(4, {
                    cause: EntityDamageCause.entityAttack,
                    damagingEntity: stormWeaver,
                });
                chainer.setOnFire(5, true);
                chainer.addTag("chainer");
                chainedZombies.push(chainer);
            }
        }
        catch (error) { }
        stormWeavers["rza:chain_length"].set(stormWeaverId, Math.max(chainLength - 1, 0));
        const nextChainer = findNextChainer(chainer, stormWeaverId);
        if (nextChainer) {
            try {
                if (nextChainer) {
                    nextChainer.applyDamage(4, {
                        cause: EntityDamageCause.entityAttack,
                        damagingEntity: stormWeaver,
                    });
                    nextChainer.setOnFire(5, true);
                    nextChainer.addTag("chainer");
                    chainedZombies.push(nextChainer);
                }
            }
            catch (error) { }
            const chainedZombieIds = chainedZombies
                .map((zombie) => zombie.id)
                .join(",");
            stormWeaver.setDynamicProperty(`storm_weaver${stormWeaverId}chained_zombies_data`, chainedZombieIds);
            visualizeLightningEffect(chainer, nextChainer);
        }
        else {
            for (const zombie of chainedZombies) {
                try {
                    if (zombie) {
                        zombie.removeTag("chainer");
                    }
                }
                catch (error) { }
            }
            chainedZombies.length = 0;
        }
    }
    return;
}
function findNextChainer(chainer, stormWeaverId) {
    scheduleChainerTagRemoval(chainer, stormWeaverId);
    try {
        return chainer.dimension.getEntities({
            location: chainer.location,
            families: ["zombie"],
            excludeTags: ["chainer"],
            minDistance: 3,
            maxDistance: 24,
            closest: 1,
        })[0];
    }
    catch (error) {
        return undefined;
    }
}
function visualizeLightningEffect(chainer, nextChainer) {
    const chainerLocation = { ...chainer.location, y: chainer.location.y + 0.5 };
    const nextChainerLocation = {
        ...nextChainer.location,
        y: nextChainer.location.y + 0.5,
    };
    const distance = calculateDistance(chainer.location, nextChainer.location);
    const positions = fixedPosRaycast(chainerLocation, nextChainerLocation, distance, 0.5);
    for (const pos of positions) {
        try {
            chainer.dimension.spawnParticle("rza:lightning", pos);
        }
        catch (e) { }
    }
    return;
}
export function restoreChainedZombies(stormWeaver) {
    try {
        const stormWeaverId = stormWeaver.id;
        const dataKey = `storm_weaver${stormWeaverId}chained_zombies_data`;
        const chainedZombieIds = stormWeaver.getDynamicProperty(dataKey);
        if (typeof chainedZombieIds === "string" && chainedZombieIds.length > 0) {
            const storedZombieIds = chainedZombieIds.split(",");
            const nearbyTaggedZombies = stormWeaver.dimension.getEntities({
                location: stormWeaver.location,
                maxDistance: 256,
                families: ["zombie"],
                tags: ["chainer"],
            });
            for (const zombie of nearbyTaggedZombies) {
                try {
                    if (zombie && storedZombieIds.includes(zombie.id)) {
                        zombie.removeTag("chainer");
                    }
                }
                catch (error) { }
            }
            if (!stormWeavers["rza:chained_zombies"].has(stormWeaver.id)) {
                stormWeavers["rza:chained_zombies"].set(stormWeaver.id, []);
            }
            const chainedZombies = stormWeavers["rza:chained_zombies"].get(stormWeaver.id);
            try {
                stormWeaver.clearDynamicProperties();
            }
            catch (error) { }
            chainedZombies.length = 0;
        }
    }
    catch (error) { }
    return;
}
function scheduleChainerTagRemoval(chainer, stormWeaverId) {
    const chainLength = stormWeavers["rza:chain_length"].get(stormWeaverId) || 0;
    if (chainLength <= 0) {
        const chainedZombies = stormWeavers["rza:chained_zombies"].get(stormWeaverId) || [];
        let removeTagDelay = system.runTimeout(() => {
            for (const zombie of chainedZombies) {
                try {
                    if (zombie) {
                        zombie.removeTag("chainer");
                    }
                }
                catch (error) { }
            }
            chainedZombies.length = 0;
            system.clearRun(removeTagDelay);
        }, 10);
    }
    else {
        let removeTagDelay = system.runTimeout(() => {
            try {
                if (chainer) {
                    chainer.removeTag("chainer");
                }
            }
            catch (error) { }
            system.clearRun(removeTagDelay);
        }, 10);
    }
    return;
}
export function cleanupStormWeaver(stormWeaver, willBeUnloaded) {
    try {
        const stormWeaverId = stormWeaver.id;
        const chainedZombies = stormWeavers["rza:chained_zombies"].get(stormWeaverId) || [];
        for (const zombie of chainedZombies) {
            try {
                if (zombie) {
                    zombie.removeTag("chainer");
                }
            }
            catch (error) { }
        }
        chainedZombies.length = 0;
        stormWeavers["rza:chained_zombies"].delete(stormWeaverId);
        stormWeavers["rza:chain_length"].delete(stormWeaverId);
        if (!willBeUnloaded) {
            try {
                stormWeaver.clearDynamicProperties();
            }
            catch (error) { }
        }
    }
    catch (error) { }
    return;
}
export function fireLightningStrike(stormWeaver) {
    const id = stormWeaver.id;
    const dimension = stormWeaver.dimension;
    const location = stormWeaver.location;
    const target = stormWeaver.target;
    if (!target)
        return;
    const targetLocation = target.location;
    const direction = calculateDirection(location, targetLocation);
    const startOffset = 1.5;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 1 + direction.y * startOffset,
        z: location.z + direction.z * startOffset,
    };
    stormWeavers["rza:chain_length"].set(id, 16);
    const positions = fixedLenRaycast(startPos, direction, 32, 0.5);
    for (const pos of positions) {
        try {
            dimension.spawnParticle("rza:lightning", pos);
            if ((stormWeavers?.["rza:chain_length"]?.get(id) ?? 0) > 0) {
                const chainer = dimension.getEntities({
                    location: pos,
                    families: ["zombie"],
                    excludeTags: ["chainer"],
                    maxDistance: 4,
                    closest: 1,
                })[0];
                if (chainer) {
                    const zombieTypeId = chainer?.typeId;
                    if (zombieTypeId === "rza:alpha" ||
                        calculateDistance(pos, chainer.location) < 2) {
                        stormWeaverLightning(chainer, stormWeaver);
                        break;
                    }
                }
            }
        }
        catch (e) { }
    }
    return;
}
