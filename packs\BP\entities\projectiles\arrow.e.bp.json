{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "minecraft:arrow", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {}}, "minecraft:hard_arrow": {"minecraft:projectile": {"on_hit": {"impact_damage": {"damage": [1, 5], "knockback": true, "semi_random_diff_damage": false, "destroy_on_hit": true}, "stick_in_ground": {"shake_time": 0.35}, "arrow_effect": {}}, "hit_sound": "bow.hit", "power": 1.6, "gravity": 0.05, "uncertainty_base": 16, "uncertainty_multiplier": 4, "anchor": 1, "should_bounce": true, "offset": [0, -0.1, 0]}}, "minecraft:player_arrow": {"minecraft:projectile": {"on_hit": {"impact_damage": {"damage": 1, "knockback": true, "semi_random_diff_damage": true, "destroy_on_hit": true, "max_critical_damage": 10, "min_critical_damage": 9, "power_multiplier": 0.97}, "stick_in_ground": {"shake_time": 0.35}, "arrow_effect": {}}, "hit_sound": "bow.hit", "power": 5, "gravity": 0.05, "uncertainty_base": 1, "uncertainty_multiplier": 0, "anchor": 1, "should_bounce": true, "offset": [0, -0.1, 0]}}, "minecraft:pillager_arrow": {"minecraft:timer": {"looping": true, "time": 8, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": [3, 6], "knockback": true, "semi_random_diff_damage": false, "destroy_on_hit": true}, "stick_in_ground": {"shake_time": 0.35}, "arrow_effect": {}}, "hit_sound": "bow.hit", "power": 3, "gravity": 0.05, "uncertainty_base": 1, "uncertainty_multiplier": 2, "anchor": 1, "should_bounce": true, "offset": [0, -0.1, 0]}}, "rza:turret_arrow": {"minecraft:timer": {"looping": true, "time": 4, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": [1, 2], "filter": "!query.has_any_family('player')", "knockback": false, "semi_random_diff_damage": false, "destroy_on_hit": true}, "arrow_effect": {}, "stick_in_ground": {"shake_time": 0.35}}, "destroy_on_hurt": true, "hit_sound": "bow.hit", "power": 2.5, "gravity": 0.05, "uncertainty_base": 1, "uncertainty_multiplier": 0, "anchor": 1, "should_bounce": true, "offset": [0, -0.7, 0]}}}, "components": {"minecraft:collision_box": {"width": 0.25, "height": 0.25}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": [1, 4], "knockback": true, "semi_random_diff_damage": false, "destroy_on_hit": true}, "stick_in_ground": {"shake_time": 0.35}, "arrow_effect": {}}, "hit_sound": "bow.hit", "power": 1.6, "gravity": 0.05, "uncertainty_base": 16, "uncertainty_multiplier": 4, "anchor": 1, "should_bounce": true, "offset": [0, -0.1, 0]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80, "max_dropped_ticks": 7, "use_motion_prediction_hints": true}}}, "events": {"minecraft:entity_spawned": {"sequence": [{"filters": {"test": "is_difficulty", "value": "hard"}, "add": {"component_groups": ["minecraft:hard_arrow"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "add": {"component_groups": ["minecraft:player_arrow"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "pillager"}, "add": {"component_groups": ["minecraft:pillager_arrow"]}}, {"filters": {"test": "is_family", "subject": "other", "value": "arrow_turret"}, "add": {"component_groups": ["rza:turret_arrow"]}}]}, "rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}}}