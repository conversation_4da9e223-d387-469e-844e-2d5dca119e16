import { system, world } from "@minecraft/server";
import "./player/playerSetup";
import "./world/index";
import "./blocks/index";
import "./items/index";
import { blockComponents } from "./blocks/index";
import { itemComponents } from "./items/index";
import { initializeEntityEvents } from "./entities/general/index";
import { mainEntityFeatures } from "./entities/general/entities";
import { messagesByDay, playDayMessages } from "./player/playerSetup";
system.beforeEvents.startup.subscribe((data) => {
    blockComponents(data.blockComponentRegistry);
    itemComponents(data.itemComponentRegistry);
    return;
});
world.afterEvents.worldLoad.subscribe(() => {
    initializeEntityEvents();
    return;
});
let worldAgeOffset = 0;
let dimensions = [];
const initRun = system.run(() => {
    worldAgeOffset = Number(world.getDynamicProperty("worldAgeOffset")) || (world.setDynamicProperty("worldAgeOffset", 0), 0);
    dimensions = ["overworld", "nether", "the_end"].map((name) => world.getDimension(name));
    system.clearRun(initRun);
});
system.runTimeout(() => {
    system.runInterval(() => {
        const worldAge = world.getDay();
        if (worldAge > worldAgeOffset) {
            world.setDynamicProperty("worldAgeOffset", worldAge);
            const titleColor = worldAge === 100 ? "§4" : "§2";
            dimensions.forEach((dimension) => {
                dimension.runCommand(`title @a title Day ${titleColor}${worldAge}§r`);
            });
            world.sendMessage(`Current Day: Day ${titleColor}${worldAge}§r`);
            if (messagesByDay[worldAge]) {
                for (const player of world.getAllPlayers()) {
                    const heardDays = player.getDynamicProperty("heard_message_days");
                    const heardDaysArray = heardDays ? JSON.parse(heardDays) : [];
                    if (!heardDaysArray.includes(worldAge)) {
                        playDayMessages(player, worldAge, 100);
                        heardDaysArray.push(worldAge);
                        player.setDynamicProperty("heard_message_days", JSON.stringify(heardDaysArray));
                    }
                }
            }
            if (worldAge >= 100) {
                const mutatedZombies = world.scoreboard.getObjective("mutated_zombies");
                if (mutatedZombies)
                    mutatedZombies.setScore("main", 1);
            }
            worldAgeOffset = worldAge;
        }
        dimensions.forEach((dimension) => {
            const entities = dimension.getEntities();
            entities.forEach((entity) => {
                mainEntityFeatures(entity);
            });
        });
        return;
    });
    return;
}, 200);
system.afterEvents.scriptEventReceive.subscribe((data) => {
    const id = data.id;
    if (id === "rza:day200") {
        world.setAbsoluteTime(4800000);
    }
    return;
});
