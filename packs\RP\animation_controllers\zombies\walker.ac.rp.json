{"format_version": "1.10.0", "animation_controllers": {"controller.animation.walker.normal.general": {"states": {"default": {"animations": [{"idle1": "variable.idle_anim==1"}, {"idle2": "variable.idle_anim==2"}, {"idle3": "variable.idle_anim==3"}, {"idle4": "variable.idle_anim==4"}, {"idle5": "variable.idle_anim==5"}], "on_entry": ["variable.idle_anim=math.random_integer(1,5);"], "transitions": [{"walking": "query.ground_speed>0.5"}], "blend_transition": 0.2}, "walking": {"animations": [{"walk1": "variable.walk_anim==1"}, {"walk2": "variable.walk_anim==2"}, {"walk3": "variable.walk_anim==3"}, {"walk4": "variable.walk_anim==4"}, {"walk5": "variable.walk_anim==5"}], "on_entry": ["variable.walk_anim=math.random_integer(1,5);"], "transitions": [{"default": "query.ground_speed<0.3"}], "blend_transition": 0.3}}}, "controller.animation.walker.normal.damage": {"states": {"default": {"transitions": [{"damaged": "q.property('rza:damage') == true"}], "blend_transition": 0.2}, "damaged": {"animations": ["damage"], "transitions": [{"default": "q.all_animations_finished"}], "blend_transition": 0.2}}}, "controller.animation.walker.normal.attack": {"states": {"default": {"transitions": [{"attacking": "v.attack_time > 0"}], "blend_transition": 0.2}, "attacking": {"animations": [{"attack1": "variable.attack_anim==1"}, {"attack2": "variable.attack_anim==2"}, {"attack3": "variable.attack_anim==3"}], "on_entry": ["variable.attack_anim=math.random_integer(1,3);"], "transitions": [{"default": "v.attack_time <= 0 && query.any_animation_finished"}], "blend_transition": 0.2}}}, "controller.animation.walker.normal.death": {"states": {"default": {"animations": ["look_at_target", "general", "attack_controller"], "transitions": [{"death": "!query.is_alive"}], "blend_transition": 0.2}, "death": {"animations": [{"death_rot": "variable.death_anim==1", "death1": "variable.death_anim==1"}, {"death_rot": "variable.death_anim==2", "death2": "variable.death_anim==2"}, {"death_rot": "variable.death_anim==3", "death3": "variable.death_anim==3"}, {"death_rot": "variable.death_anim==4", "death4": "variable.death_anim==4"}], "on_entry": ["variable.death_anim=math.random_integer(1,4);"], "particle_effects": [{"effect": "blood", "locator": "blood"}]}}}}}