{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:zombie_villager_v2", "materials": {"default": "zombie_villager_v2", "masked": "zombie_villager_v2_masked"}, "textures": {"default": "textures/entity/zombie_villager2/zombie-villager", "desert": "textures/entity/zombie_villager2/biomes/biome-desert-zombie", "jungle": "textures/entity/zombie_villager2/biomes/biome-jungle-zombie", "plains": "textures/entity/zombie_villager2/biomes/biome-plains-zombie", "savanna": "textures/entity/zombie_villager2/biomes/biome-savanna-zombie", "snow": "textures/entity/zombie_villager2/biomes/biome-snow-zombie", "swamp": "textures/entity/zombie_villager2/biomes/biome-swamp-zombie", "taiga": "textures/entity/zombie_villager2/biomes/biome-taiga-zombie", "armorer": "textures/entity/zombie_villager2/professions/armorer", "butcher": "textures/entity/zombie_villager2/professions/butcher", "cartographer": "textures/entity/zombie_villager2/professions/cartographer", "cleric": "textures/entity/zombie_villager2/professions/cleric", "farmer": "textures/entity/zombie_villager2/professions/farmer", "fisherman": "textures/entity/zombie_villager2/professions/fisherman", "fletcher": "textures/entity/zombie_villager2/professions/fletcher", "leatherworker": "textures/entity/zombie_villager2/professions/leatherworker", "librarian": "textures/entity/zombie_villager2/professions/librarian", "shepherd": "textures/entity/zombie_villager2/professions/shepherd", "tool_smith": "textures/entity/zombie_villager2/professions/toolsmith", "weapon_smith": "textures/entity/zombie_villager2/professions/weaponsmith", "stonemason": "textures/entity/zombie_villager2/professions/stonemason", "nitwit": "textures/entity/zombie_villager2/professions/nitwit"}, "geometry": {"default": "geometry.zombie.villager_v2.rza", "death": "geometry.zombie.villager_v2_death.rza"}, "animations": {"look_at_target": "animation.common.look_at_target", "baby_transform": "animation.villager.baby_transform", "idle1": "animation.walker.normal.idle1", "idle2": "animation.walker.normal.idle2", "idle3": "animation.walker.normal.idle3", "idle4": "animation.walker.normal.idle4", "idle5": "animation.walker.normal.idle5", "walk1": "animation.walker.normal.walk1", "walk2": "animation.walker.normal.walk2", "walk3": "animation.walker.normal.walk3", "walk4": "animation.walker.normal.walk4", "walk5": "animation.walker.normal.walk5", "attack1": "animation.walker.normal.attack1", "attack2": "animation.walker.normal.attack2", "attack3": "animation.walker.normal.attack3", "death1": "animation.walker.normal.death1", "death2": "animation.walker.normal.death2", "death3": "animation.walker.normal.death3", "death4": "animation.walker.normal.death4", "death_rot": "animation.general.death_rot", "general": "controller.animation.walker.normal.general", "attack_controller": "controller.animation.walker.normal.attack", "death_controller": "controller.animation.walker.normal.death"}, "scripts": {"pre_animation": ["variable.num_professions = 14;", "variable.profession_index = ((query.variant < variable.num_professions) ? query.variant : 0);", "variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"], "animate": [{"baby_transform": "q.is_baby"}, "death_controller"]}, "render_controllers": ["controller.render.zombie_villager_v2_base", "controller.render.zombie_villager_v2_masked"], "particle_effects": {"blood": "rza:blood"}, "enable_attachables": true, "spawn_egg": {"texture": "spawn_egg", "texture_index": 42}}}}