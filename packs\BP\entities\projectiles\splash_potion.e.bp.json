{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "minecraft:splash_potion", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:from_villager": {"minecraft:projectile": {"on_hit": {"douse_fire": {}, "thrown_potion_effect": {}, "remove_on_hit": {}}, "power": 0.5, "offset": [0, 1.5, 0], "gravity": 0.05, "angle_offset": -45, "hit_sound": "glass"}}}, "events": {"minecraft:entity_spawned": {"sequence": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}, "add": {"component_groups": ["rza:from_villager"]}}]}}, "components": {"minecraft:collision_box": {"width": 0.25, "height": 0.25}, "minecraft:projectile": {"on_hit": {"douse_fire": {}, "thrown_potion_effect": {}, "remove_on_hit": {}}, "power": 0.5, "gravity": 0.05, "angle_offset": -20, "hit_sound": "glass"}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80, "max_dropped_ticks": 5, "use_motion_prediction_hints": true}}}}}