{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.walker.normal", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 7, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "spine", "parent": "root", "pivot": [0, 12, 0]}, {"name": "head", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "body", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}], "locators": {"blood": {"offset": [0, 23, 0], "rotation": [-90, 0, 0]}}}, {"name": "leftArm", "parent": "spine", "pivot": [5, 22, 0], "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [32, 48]}]}, {"name": "leftItem", "parent": "leftArm", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "spine", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1], "locators": {"lead_hold": [-6, 15, 1]}}, {"name": "waist", "parent": "root", "pivot": [0, 12, 0]}, {"name": "left_leg", "parent": "waist", "pivot": [1.9, 12, 0], "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 12, 4], "uv": [16, 48]}]}, {"name": "right_leg", "parent": "waist", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}]}, {"description": {"identifier": "geometry.walker.normal.death", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 7, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "spine", "parent": "root", "pivot": [0, 12, 0]}, {"name": "head", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "body", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}], "locators": {"blood": {"offset": [0, 23, 0], "rotation": [-90, 0, 0]}}}, {"name": "leftArm", "parent": "root", "pivot": [5, 22, 0], "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [32, 48]}]}, {"name": "leftItem", "parent": "leftArm", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "root", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1], "locators": {"lead_hold": [-6, 15, 1]}}, {"name": "waist", "parent": "root", "pivot": [0, 0, 0]}, {"name": "left_leg", "parent": "waist", "pivot": [1.89791, 0, 0], "cubes": [{"origin": [-0.10209, 0, -2], "size": [4, 12, 4], "uv": [16, 48]}]}, {"name": "right_leg", "parent": "waist", "pivot": [-1.90209, 0, 0], "cubes": [{"origin": [-3.90209, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}]}]}