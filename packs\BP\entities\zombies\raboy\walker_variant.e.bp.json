{
  "format_version": "1.21.90",
  "minecraft:entity": {
    "description": {
      "identifier": "rza:walker_variant",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false,
      "properties": {
        "rza:mutated": {
          "client_sync": true,
          "type": "bool",
          "default": false
        }
      }
    },
    "component_groups": {
      "rza:walker_regular": {
        "minecraft:health": {
          "value": 15
        },
        "minecraft:attack": {
          "damage": 3
        },
        "minecraft:behavior.delayed_attack": {
          "priority": 3,
          "attack_duration": 0.1,
          "hit_delay_pct": 0.85,
          "max_path_time": 1,
          "random_stop_interval": 120,
          "x_max_rotation": 30
        },
        "minecraft:experience_reward": {
          "on_death": "query.last_hit_by_player?2+(query.equipment_count*math.random(1,3)):0"
        }
      },
      "rza:walker_mutated": {
        "minecraft:health": {
          "value": 30
        },
        "minecraft:attack": {
          "damage": 4
        },
        "minecraft:behavior.delayed_attack": {
          "priority": 3,
          "speed_multiplier": 1.7,
          "attack_duration": 0.5,
          "hit_delay_pct": 0.85,
          "max_path_time": 1,
          "random_stop_interval": 120,
          "x_max_rotation": 30
        },
        "minecraft:experience_reward": {
          "on_death": "query.last_hit_by_player?2+(query.equipment_count*math.random(2,5)):0"
        }
      },
      "rza:alert_others": {
        "minecraft:angry": {
          "broadcast_anger": true,
          "broadcast_range": 48,
          "broadcast_targets": ["zombie"],
          "duration": 8,
          "broadcast_filters": {
            "all_of": [
              {
                "test": "has_target",
                "subject": "other",
                "operator": "!=",
                "value": false
              },
              {
                "none_of": [
                  {
                    "test": "has_mob_effect",
                    "subject": "other",
                    "value": "weakness"
                  }
                ]
              },
              {
                "test": "is_family",
                "subject": "other",
                "operator": "!=",
                "value": "feral"
              }
            ]
          },
          "filters": {
            "all_of": [
              {
                "test": "is_visible",
                "subject": "other",
                "value": true
              },
              {
                "test": "is_family",
                "subject": "other",
                "operator": "!=",
                "value": "zombie"
              }
            ]
          },
          "calm_event": {
            "event": "rza:idle",
            "target": "self"
          }
        }
      }
    },
    "events": {
      "minecraft:entity_transformed": {
        "sequence": [
          {
            "add": {
              "component_groups": ["rza:walker_regular"]
            }
          },
          {
            "queue_command": {
              "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate"
            }
          }
        ]
      },
      "rza:mutate": {
        "sequence": [
          //When not on full moon
          {
            "filters": {
              "test": "moon_phase",
              "operator": "!=",
              "value": 0
            },
            "randomize": [
              {
                "weight": 100
              },
              {
                "weight": 20,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "easy"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              },
              {
                "weight": 40,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "normal"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              },
              {
                "weight": 60,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "hard"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              }
            ]
          },
          //During full moon
          {
            "filters": {
              "test": "moon_phase",
              "operator": "==",
              "value": 0
            },
            "randomize": [
              {
                "weight": 100
              },
              {
                "weight": 40,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "easy"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              },
              {
                "weight": 60,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "normal"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              },
              {
                "weight": 80,
                "filters": {
                  "test": "is_difficulty",
                  "operator": "==",
                  "value": "hard"
                },
                "sequence": [
                  {
                    "set_property": {
                      "rza:mutated": true
                    }
                  },
                  {
                    "remove": {
                      "component_groups": ["rza:walker_regular"]
                    }
                  },
                  {
                    "add": {
                      "component_groups": ["rza:walker_mutated"]
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      "rza:alert_others": {
        "add": {
          "component_groups": ["rza:alert_others"]
        }
      },
      "rza:idle": {
        "remove": {
          "component_groups": ["rza:alert_others"]
        }
      }
    },
    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:nameable": {},
      "minecraft:type_family": {
        "family": ["zombie", "walker", "undead", "monster", "mob"]
      },
      "minecraft:collision_box": {
        "width": 0.6,
        "height": 1.9
      },
      "minecraft:movement": {
        "value": 0.2
      },
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "is_amphibious": true,
        "can_pass_doors": true,
        "can_walk": true,
        "can_float": true,
        "can_path_over_water": true,
        "avoid_damage_blocks": false
      },
      "minecraft:jump.static": {},
      "minecraft:behavior.float": {
        "priority": 5
      },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": {
              "test": "in_lava",
              "subject": "self",
              "operator": "==",
              "value": true
            },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0,
        "breathes_air": true,
        "breathes_water": true
      },
      "minecraft:loot": {
        "table": "loot_tables/entities/zombies/walker.lt.json"
      },
      "minecraft:despawn": {
        "despawn_from_distance": {
          "max_distance": 64,
          "min_distance": 32
        }
      },
      "minecraft:behavior.random_stroll": {
        "priority": 7,
        "speed_multiplier": 1
      },
      "minecraft:behavior.look_at_player": {
        "priority": 8,
        "look_distance": 6,
        "probability": 0.02
      },
      "minecraft:behavior.random_look_around": {
        "priority": 9
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": {
              "filters": {
                "any_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "zombie"
                  }
                ]
              }
            },
            "deals_damage": "no"
          }
        ]
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "alert_same_type": true,
        "entity_types": {
          "filters": {
            "all_of": [
              {
                "test": "is_family",
                "subject": "other",
                "operator": "!=",
                "value": "turret"
              },
              {
                "test": "is_visible",
                "subject": "other",
                "value": true
              }
            ]
          }
        }
      },
      "minecraft:follow_range": {
        "value": 64
      },
      "minecraft:dweller": {
        "dwelling_type": "village",
        "dweller_role": "hostile",
        "update_interval_base": 60,
        "update_interval_variant": 40,
        "can_find_poi": false,
        "can_migrate": true,
        "first_founding_reward": 0
      },
      "minecraft:behavior.move_to_village": {
        "priority": 4,
        "speed_multiplier": 1,
        "goal_radius": 2
      },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 5,
        "must_see": true,
        "reselect_targets": true,
        "must_see_forget_duration": 0,
        "scan_interval": 10,
        "entity_types": [
          {
            "filters": {
              "all_of": [
                {
                  "test": "is_visible",
                  "subject": "other",
                  "value": true
                },
                {
                  "any_of": [
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "player"
                    },
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "snowgolem"
                    },
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "irongolem"
                    },
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "villager"
                    },
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "wandering_trader"
                    },
                    {
                      "test": "is_family",
                      "subject": "other",
                      "value": "illager"
                    }
                  ]
                }
              ]
            },
            "max_dist": 24
          }
        ]
      },
      "minecraft:on_target_acquired": {
        "event": "rza:alert_others",
        "target": "self"
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      },
      "minecraft:physics": {},
      "minecraft:conditional_bandwidth_optimization": {},
      "minecraft:ambient_sound_interval": {
        "event_name": "ambient",
        "range": 10,
        "value": 7
      }
    }
  }
}
