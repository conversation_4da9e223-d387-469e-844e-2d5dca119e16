{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:supercharged_iron_golem", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {}, "events": {"minecraft:entity_transformed": {"sequence": [{"queue_command": {"command": ["playsound mob.irongolem.repair @p ~ ~ ~ 3 1"]}}, {"queue_command": {"command": ["playsound mob.charged_iron_golem.upgrade @p ~ ~ ~ 3 1"]}}]}, "rza:heal": {"queue_command": {"command": "effect @s regeneration 2 5"}}, "rza:explode": {"queue_command": {"command": ["summon rza:iron_golem_explosions"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["supercharged_irongolem", "irongolem", "mob"]}, "minecraft:nameable": {}, "minecraft:collision_box": {"width": 1, "height": 2.9}, "minecraft:loot": {"table": "loot_tables/entities/iron_golem.json"}, "minecraft:health": {"value": 800}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.18}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "is_amphibious": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "swing": true, "health_amount": 25, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "rza:sonic_cannon_item"}, {"test": "rider_count", "operator": "<", "value": 2}]}, "event": "rza:attach_sonic_cannon", "target": "self"}, "use_item": true, "swing": true, "play_sounds": "irongolem.repair", "interact_text": "action.interact.attach_turret"}]}, "minecraft:attack": {"damage": {"range_min": 7, "range_max": 21}}, "minecraft:damage_sensor": {"triggers": [{"cause": "fall", "deals_damage": "no"}, {"cause": "block_explosion", "deals_damage": "no"}, {"cause": "entity_explosion", "deals_damage": "no"}, {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}]}, "minecraft:rideable": {"family_types": ["turret"], "seat_count": 2, "seats": [{"position": [0.6, 1.8, 0.1], "rotate_rider_by": -10}, {"position": [-0.6, 1.8, 0.1], "rotate_rider_by": 10}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:leashable": {"soft_distance": 4, "hard_distance": 6, "max_distance": 10}, "minecraft:balloonable": {"mass": 2}, "minecraft:behavior.delayed_attack": {"priority": 2, "attack_duration": 0.5, "cooldown_time": 1, "hit_delay_pct": 0.7, "speed_multiplier": 1.3, "require_complete_path": true, "track_target": false}, "minecraft:behavior.random_stroll": {"priority": 6, "xz_dist": 16}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "must_reach": true, "persist_time": 0, "reselect_targets": true, "within_radius": 12, "reevaluate_description": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 12}]}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:follow_range": {"value": 64}, "minecraft:on_death": {"event": "rza:explode", "target": "self"}, "minecraft:conditional_bandwidth_optimization": {}}}}