import { Vector3 } from "@minecraft/server";

/**
 * Calculates the Euclidean distance between two 3D points
 * @param {Vector3} loc1 - The first position vector
 * @param {Vector3} loc2 - The second position vector
 * @returns {number} The distance between the two points in blocks
 * @description Used primarily for repair array calculations. Distance is calculated in 1-block steps.
 */
export function calculateDistance(loc1: Vector3, loc2: Vector3): number {
  const dx = loc1.x - loc2.x;
  const dy = loc1.y - loc2.y;
  const dz = loc1.z - loc2.z;

  //Distance calculation will be 1-block step
  return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Performs a raycast between two fixed positions
 * @param {Vector3} from - Starting position of the raycast
 * @param {Vector3} to - End position of the raycast
 * @param {number} distance - Maximum distance of the raycast in blocks
 * @param {number} step - Step size between each point (defaults to 1)
 * @returns {Vector3[]} Array of positions along the ray
 */
export function fixedPosRaycast(
  from: Vector3,
  to: Vector3,
  distance: number,
  step: number,
): Vector3[] {
  const positions: Vector3[] = [];

  for (let i = 0; i <= distance; i += step) {
    const t = i / distance; // Normalized interpolation factor
    positions.push({
      x: from.x + t * (to.x - from.x),
      y: from.y + t * (to.y - from.y),
      z: from.z + t * (to.z - from.z),
    });
  }

  return positions;
}

/**
 * Performs a directional raycast with a fixed length and returns an array of positions
 * @param {Vector3} from - Starting position of the raycast
 * @param {Vector3} direction - Direction vector of the raycast
 * @param {number} length - Length of the raycast in blocks
 * @param {number} step - Step size between each point (defaults to 1)
 * @returns {Vector3[]} Array of positions along the ray
 */
export function fixedLenRaycast(
  from: Vector3,
  direction: Vector3,
  length: number,
  step: number,
): Vector3[] {
  const positions: Vector3[] = [];

  for (let i = 0; i <= length; i += step) {
    positions.push({
      x: from.x + direction.x * i,
      y: from.y + direction.y * i,
      z: from.z + direction.z * i,
    });
  }

  return positions;
}
