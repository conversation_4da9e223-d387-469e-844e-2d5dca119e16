{"format_version": "1.10.0", "animation_controllers": {"controller.animation.pulsar_system.general": {"states": {"default": {"transitions": [{"idle": "q.property('rza:active') == true && q.property('rza:active_state') == true"}], "blend_transition": 3, "blend_via_shortest_path": true}, "idle": {"animations": ["idle", "ambient_sound"], "transitions": [{"fire": "q.property('rza:fire') == true"}, {"default": "q.property('rza:active') == true && q.property('rza:active_state') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "fire": {"animations": ["fire"], "transitions": [{"idle": "q.property('rza:fire') == false"}], "blend_transition": 3, "blend_via_shortest_path": true}}}}}