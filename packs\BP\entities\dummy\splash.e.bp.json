{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:splash", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:splash": {"minecraft:type_family": {"family": ["dummy", "splash", "irongolem"]}, "minecraft:behavior.knockback_roar": {"priority": 0, "duration": 0.1, "attack_time": 0.09, "knockback_range": 2, "cooldown_time": 2, "knockback_damage": 5, "knockback_horizontal_strength": 1, "knockback_vertical_strength": 8, "damage_filters": {"none_of": [{"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "pillager"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "dummy"}, {"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "item"}]}, "knockback_filters": {"none_of": [{"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "pillager"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "dummy"}, {"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "item"}]}}, "minecraft:timer": {"time": 0.3, "time_down_event": {"event": "rza:despawn", "target": "self"}}}, "rza:attack_despawn": {"minecraft:instant_despawn": {"remove_child_entities": false}}}, "events": {"rza:splash": {"add": {"component_groups": ["rza:splash"]}}, "rza:despawn": {"add": {"component_groups": ["rza:attack_despawn"]}}}, "components": {"minecraft:type_family": {"family": ["splash"]}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:collision_box": {"height": 0.001, "width": 0.001}, "minecraft:timer": {"time": 0.5, "time_down_event": {"event": "rza:splash", "target": "self"}}, "minecraft:physics": {"push_towards_closest_space": false}, "minecraft:conditional_bandwidth_optimization": {}}}}