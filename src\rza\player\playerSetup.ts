import { system, world, Player } from "@minecraft/server";
import { meleeWeaponCooldown } from "rza/weapons/melee";

export type MessagesByDay = {
  [key: number]: string[];
};

// Message categories by day
export const messagesByDay: MessagesByDay = {
  0: [
    "§c[EMERGENCY BROADCAST] Multiple cities dark. Communication networks failing.§r",
    "§c[CDC ALERT] Unknown contagion spreading at unprecedented rate. Quarantine protocols initiated.§r",
    "§c[MILITARY DISPATCH] Massive civilian casualties. Safe zones being overrun.§r",
    "§c[EMERGENCY SERVICES] Evacuation routes compromised. Avoid population centers.§r",
    "§c[CIVIL DEFENSE] Standard containment measures failing. God help us all.§r",
    "§6[SURVIVOR RADIO] They move faster than anything we've seen. Stay in groups.§r",
    "§6[MEDICAL REPORT] Conventional weapons ineffective. Specialized equipment required.§r",
    "§6[MILITARY INTEL] Defense perimeters established. Automated systems online.§r",
    "§6[EMERGENCY ALERT] The infected are evolving. Previous protocols obsolete.§r",
    "§6[FINAL BROADCAST] If anyone can hear this... there's something wrong with their eyes...§r",
  ],
  2: [
    "§6[SURVIVOR LOG] First wave of infected identified. Basic walker variants confirmed.§r",
    "§6[MILITARY CHANNEL] Infected showing coordinated movement patterns.§r",
    "§6[FIELD REPORT] Subjects retain basic motor functions and environmental awareness.§r",
    "§6[EMERGENCY BAND] Defense systems adapting to movement patterns.§r",
    "§6[SURVIVOR NOTE] They remember patrol routes. Constant relocation required.§r",
    "§6[RECON REPORT] Automated defenses proving effective against grouped targets.§r",
    "§6[QUARANTINE LOG] Multiple defensive measures required for containment.§r",
    "§6[TACTICAL UPDATE] Converting contaminated materials for resource recovery.§r",
    "§6[MEDICAL ANALYSIS] Infected showing resistance to standard munitions.§r",
    "§6[EMERGENCY COMMS] Close-range engagement highly discouraged.§r",
    "§6[SURVIVOR RADIO] They hunt in larger groups now. Single targets are safer.§r",
    "§6[DEFENSE LOG] Automated targeting systems tracking movement patterns.§r",
    "§6[LAST TRANSMISSION] Keep moving. Stay alert. Trust the defense grid.§r",
  ],
  15: [
    "§6[UNDERGROUND REPORT] New infected variant detected in mining operations.§r",
    "§6[SURVIVOR LOG] They are breaching from below. Using mining equipment.§r",
    "§6[MAINTENANCE ALERT] Underground defense networks compromised.§r",
    "§6[TUNNEL SECURITY] Defense effectiveness reduced in confined spaces.§r",
    "§6[EMERGENCY COMMS] Lost contact with mining outposts.§r",
    "§6[MILITARY INTEL] Infected retaining tool use abilities.§r",
    "§6[ENGINEER LOG] Underground containment systems critical.§r",
    "§6[SURVIVOR REPORT] Multiple breach points detected.§r",
    "§6[HAZARD WARNING] Close-range weapons effective in tunnels.§r",
    "§6[FINAL REPORT] Strategic tunneling behavior observed.§r",
    "§6[EMERGENCY ALERT] Surface breaches imminent. Prepare topside defenses.§r",
    "§6[LAST WARNING] They are creating a network underground.§r",
  ],
  30: [
    "§6[TACTICAL ALERT] High-mobility infected emerging. Enhanced agility confirmed.§r",
    "§6[MILITARY INTEL] Feral variants displaying advanced movement capabilities.§r",
    "§6[FIELD REPORT] Vertical threats confirmed. No safe elevation.§r",
    "§6[SURVIVOR LOG] They can reach the rooftops now.§r",
    "§6[RECON DATA] Previous safe zones compromised by climbing ability.§r",
    "§6[COMBAT LOG] Standard barricades insufficient against vertical movement.§r",
    "§6[TACTICAL UPDATE] Defense systems struggling with rapid mobility.§r",
    "§6[FIELD ANALYSIS] They are learning to bypass elevated defenses.§r",
    "§6[FINAL WARNING] No elevation is safe. They can reach anywhere.§r",
  ],
  50: [
    "§6[BIOHAZARD ALERT] Acid-producing variant detected. New threat level.§r",
    "§6[CDC REPORT] Corrosive compound identified in new strain.§r",
    "§6[HAZMAT WARNING] Standard protection dissolving on contact.§r",
    "§6[LAB ANALYSIS] Spitter variants confirmed. Extreme caution advised.§r",
    "§6[FIELD STUDY] Acid effect radius expanding with each encounter.§r",
    "§6[MEDICAL ALERT] Fatal tissue damage from prolonged exposure.§r",
    "§6[TACTICAL WARNING] Defense structures vulnerable to corrosion.§r",
    "§6[SURVIVOR LOG] Contaminated zones expanding. Avoid puddles.§r",
    "§6[ENVIRONMENTAL REPORT] Ground water showing signs of contamination.§r",
    "§6[CHEMICAL ANALYSIS] Acid potency increasing over time.§r",
    "§6[EMERGENCY ALERT] Defense systems failing against corrosive attacks.§r",
    "§6[FINAL REPORT] They are dissolving our final barriers.§r",
  ],
  70: [
    "§4[EXTINCTION EVENT] Alpha strain emerged. Maximum threat level.§r",
    "§4[FINAL PROTOCOL] New apex predator controlling the horde.§r",
    "§4[SURVIVOR ALERT] Spitters breaching final defensive lines.§r",
    "§4[MILITARY BLACKBOX] Ferals overwhelming vertical defenses.§r",
    "§4[EMERGENCY OVERRIDE] Miners compromising structural integrity.§r",
    "§4[FINAL ANALYSIS] Walker swarms moving with tactical precision.§r",
    "§4[DOOMSDAY CLOCK] All defense systems failing simultaneously.§r",
    "§4[LAST STAND] Cannot track rate of tactical adaptation.§r",
    "§4[FINAL LOG] Complete defense grid failure.§r",
    "§4[FINAL REPORT] Alpha-led extinction event in progress.§r",
  ],
  100: [
    "§c[DEFCON 1 ALERT] Mass mutation event detected. All variants evolving.§r",
    "§c[CDC EMERGENCY] Unprecedented changes in infected behavior.§r",
    "§c[MILITARY OVERRIDE] Defense systems overwhelmed by enhanced threats.§r",
    "§c[FIELD REPORT] All known variants showing accelerated evolution.§r",
    "§c[SURVIVOR NETWORK] Coordinated attacks between different strains.§r",
    "§c[MEDICAL ALERT] Enhanced regeneration observed in all types.§r",
    "§c[TACTICAL UPDATE] Defense grid requiring constant recalibration.§r",
    "§c[RESEARCH LOG] Evolution patterns suggest emergent intelligence.§r",
    "§c[DEFENSE NETWORK] Containment zones failing across all sectors.§r",
    "§c[EMERGENCY BROADCAST] Combat efficiency exceeding defensive capabilities.§r",
    "§c[FINAL WARNING] Something is controlling their evolution.§r",
    "§c[LAST TRANSMISSION] The end begins.§r",
  ],
};

/**
 * Plays a sequence of messages for a specific day's events
 * @param player The player to send messages to
 * @param day The day number to play messages for
 * @param baseDelay Initial delay before starting messages
 * @returns Total duration of messages for this day
 */
export function playDayMessages(
  player: Player,
  day: number,
  baseDelay: number,
): number {
  const messages = messagesByDay[day];
  if (!messages) return 0;

  messages.forEach((message: string, index: number) => {
    const messageDelay = baseDelay + index * 200; // 10 seconds between messages
    const timeout = system.runTimeout(() => {
      // Play static first
      player.playSound("world.radio_static", { volume: 1.0, pitch: 1.0 });

      // Send message after static
      const messageTimeout = system.runTimeout(() => {
        player.sendMessage(message);
        system.clearRun(messageTimeout);
      }, 20);

      system.clearRun(timeout);
    }, messageDelay);
  });

  // Return total duration of all messages for this day
  return messages.length * 100;
}

/**
 * Sets up event handlers and initialization for new players joining the game.
 * This includes welcome messages, cooldown initialization, and other first-time setup tasks.
 * The function recursively re-registers itself to handle future player joins.
 */
function playerSetup() {
  const onSpawn = world.afterEvents.playerSpawn.subscribe((spawn) => {
    const player = spawn.player;
    const initialSpawn = spawn.initialSpawn;

    // Only run setup for first-time spawns
    if (initialSpawn) {
      // Display welcome sequence with staged timing
      let title = system.runTimeout(() => {
        // Show title screen overlay
        if (!player.hasTag("displayedTitle")) {
          player.onScreenDisplay.setTitle("title");
          player.addTag("displayedTitle");
        }

        // First welcome message after title
        let sysmes = system.runTimeout(() => {
          player.sendMessage(
            `[SYSTEM] Welcome to §6Raboy's §2Zombie §4Apocalypse§r!`,
          );

          // Second welcome message
          let sysmes2 = system.runTimeout(() => {
            player.sendMessage(`[SYSTEM] Good luck and have fun!`);
            system.clearRun(sysmes2);

            // Start playing historical messages after welcome
            const currentDay = world.getDay();
            const heardDays = player.getDynamicProperty("heard_message_days");
            const heardDaysArray = heardDays
              ? JSON.parse(heardDays as string)
              : [];

            // Play messages sequentially with proper delays
            let totalDelay = 300; // Initial delay after welcome message
            const daysToPlay = Object.keys(messagesByDay)
              .map(Number)
              .sort((a, b) => a - b)
              .filter(
                (day) => day <= currentDay && !heardDaysArray.includes(day),
              );

            // Play each day's messages sequentially
            daysToPlay.forEach((day) => {
              const duration = playDayMessages(player, day, totalDelay);
              totalDelay += duration + 300; // Add 15-second gap between days

              // Mark day as heard after its messages are scheduled
              const markHeardTimeout = system.runTimeout(() => {
                heardDaysArray.push(day);
                player.setDynamicProperty(
                  "heard_message_days",
                  JSON.stringify(heardDaysArray),
                );
                system.clearRun(markHeardTimeout);
              }, totalDelay);
            });

            // Cleanup all timeouts
            system.clearRun(sysmes2);
            system.clearRun(sysmes);
            system.clearRun(title);
          }, 60);
        }, 140);
      }, 300);

      // Initialize player melee weapon cooldown system
      meleeWeaponCooldown.set(player.id, 0);

      // Cleanup and restart listener for next player
      world.afterEvents.playerSpawn.unsubscribe(onSpawn);
      playerSetup();
      return;
    }
    return;
  });
  return;
}

// Initialize the player setup system
playerSetup();
