import {
  Entity,
  <PERSON>tityDamageCause,
  Vector3,
  VectorXZ,
} from "@minecraft/server";

/**
 * Creates a shockwave effect that damages and knocks back nearby entities
 * @param source The entity creating the shockwave
 * @param radius The radius of the shockwave effect
 * @param maxForce Maximum knockback force at center
 * @param minForce Minimum knockback force at edge
 * @param damage Amount of damage to deal
 * @param targetFamilies Array of entity families to affect (optional)
 * @param particleEffect Particle effect to spawn (optional)
 */
export function createShockwave(
  source: Entity,
  radius: number,
  maxForce: number,
  minForce: number,
  damage: number,
  targetFamilies?: string[],
  particleEffect?: string,
) {
  const dimension = source.dimension;
  const location = source.location;

  // Get nearby entities, filtered by families if specified
  const nearbyEntities = targetFamilies
    ? dimension.getEntities({
        location,
        maxDistance: radius,
        families: targetFamilies,
      })
    : dimension.getEntities({ location, maxDistance: radius });

  // Process each entity in range
  for (const target of nearbyEntities) {
    if (target.id === source.id) continue; // Skip the source entity

    // Calculate distance and force
    const normalizedDir: Vector3 = {
      x: target.location.x - source.location.x,
      y: target.location.y - source.location.y,
      z: target.location.z - source.location.z,
    };
    const distance = Math.sqrt(
      normalizedDir.x * normalizedDir.x +
        normalizedDir.y * normalizedDir.y +
        normalizedDir.z * normalizedDir.z,
    );
    const forceMagnitude = maxForce * (1 - distance / radius) + minForce;

    // Calculate upward force
    const dirY = 0.4 + maxForce * 0.3; // Dynamic upward force based on maxForce

    // Normalize and apply force
    const magnitude = Math.sqrt(
      normalizedDir.x * normalizedDir.x +
        dirY * dirY +
        normalizedDir.z * normalizedDir.z,
    );
    const velocity = {
      x: (normalizedDir.x / magnitude) * forceMagnitude,
      y: dirY * forceMagnitude,
      z: (normalizedDir.z / magnitude) * forceMagnitude,
    };

    try {
      // Apply damage to the entity
      target.applyDamage(damage, {
        cause: EntityDamageCause.entityAttack,
        damagingEntity: source,
      });

      // Try to apply impulse first
      target.applyImpulse(velocity);
    } catch (error) {
      try {
        const dynamicDir: VectorXZ = {
          x: normalizedDir.x * forceMagnitude,
          z: normalizedDir.z * forceMagnitude,
        };
        // Fallback to knockback if applyImpulse fails
        target.applyDamage(damage, {
          cause: EntityDamageCause.entityAttack,
          damagingEntity: source,
        });
        //  Using internal knockback method as fallback
        target.applyKnockback(dynamicDir, 0.5);
      } catch (fallbackError) {
        console.warn(
          `Failed to apply knockback to entity ${target.typeId}: ${fallbackError}`,
        );
      }
    }
  }

  // Spawn particle effect if specified
  if (particleEffect) {
    dimension.spawnParticle(particleEffect, location);
  }
  return;
}
