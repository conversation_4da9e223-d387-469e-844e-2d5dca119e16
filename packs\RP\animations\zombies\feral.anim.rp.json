{"format_version": "1.10.0", "animations": {"animation.feral.look_at_target": {"loop": true, "bones": {"head": {"relative_to": {"rotation": "entity"}, "rotation": ["90+query.target_x_rotation-this", "query.target_y_rotation-this", 0]}}}, "animation.feral.idle": {"loop": true, "animation_length": 2, "bones": {"root": {"position": [0, -2, 6]}, "spine": {"rotation": [92.5, 0, 0]}, "head": {"rotation": {"0.0": [-80, 0, 0], "0.25": [-85, 0, 0], "1.0": [-85, 0, 0], "1.25": [-80, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, 0, -1], "2.0": [0, 0, 0]}}, "body": {"position": {"0.0": [0, 0, 0.25], "0.5": [0, 0, 0], "1.0": [0, 0, 0], "1.5": [0, 0, 0.25]}}, "leftArm": {"rotation": {"0.0": [-105, 0, 0], "1.0": [-105.01365, -2.41476, 0.64743], "2.0": [-105, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-105, 0, 0], "1.0": [-105.01365, 2.41476, -0.64743], "2.0": [-105, 0, 0]}}, "waist": {"rotation": [-2.5, 0, 0], "position": [0, 1, 0]}, "left_leg": {"rotation": {"0.0": [10, 0, -20], "0.75": [9.99067, 0.43399, -22.46207], "1.5": [10, 0, -20]}}, "right_leg": {"rotation": {"0.0": [10, 0, 20], "0.75": [9.99067, -0.43399, 22.46207], "1.5": [10, 0, 20]}}}}, "animation.feral.walk": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*4))", "bones": {"root": {"position": [0, 0, 10]}, "spine": {"rotation": [90, 0, 0]}, "head": {"rotation": ["-90+math.sin((q.anim_time + 3) * 600) * 4", 0, 0], "position": [0, 0, "-1+math.sin((q.anim_time + 4) * 600) * 0.5"]}, "body": {"rotation": ["math.sin(q.anim_time * 600) * 4", 0, 0], "position": [0, 0, "-1+math.sin((q.anim_time + 3.4) * 600) * 0.3"]}, "leftArm": {"rotation": ["-99.9774+math.sin((q.anim_time + 7.1) * 300) * 32", 1.17339, -2.2077], "position": [0, "-0.25+math.clamp((math.sin((q.anim_time + 2) * 300) * 0.5), -0.3, 0.5)", "-2+math.clamp((math.sin((q.anim_time + 2) * 300) * 2), 0, 4)"]}, "rightArm": {"rotation": ["-99.9774-math.sin((q.anim_time + 7.1) * 300) * 32", 1.17339, -2.2077], "position": [0, "-0.25+math.clamp((-math.sin((q.anim_time + 2) * 300) * 0.5), -0.3, 0.5)", "-2+math.clamp((-math.sin((q.anim_time + 2) * 300) * 2), 0, 4)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [1, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-1.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [-1, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.feral.run": {"loop": true, "animation_length": 0.75, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"root": {"position": {"0.0": [0, -2, 7], "0.2083": [0, -1, 7], "0.5833": [0, 0, 7], "0.75": [0, -2, 7]}}, "spine": {"rotation": {"0.0": [70, 0, 0], "0.2083": [97.5, 0, 0], "0.375": [88.33, 0, 0], "0.75": [70, 0, 0]}}, "head": {"rotation": {"0.0": [-70, 0, 0], "0.2083": [-97.5, 0, 0], "0.375": [-65, 0, 0], "0.5833": [-70, 0, 0], "0.75": [-70, 0, 0]}, "position": [0, 0, 0]}, "body": {"position": [0, 0, 0.25]}, "leftArm": {"rotation": {"0.0": [-162.5, 0, 0], "0.2083": [-85, 0, 0], "0.2917": [-60, 0, 0], "0.375": [-47.5, 0, 0], "0.6667": [-120, 0, 0], "0.75": [-162.5, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-120, 0, 0], "0.0833": [-162.5, 0, 0], "0.2917": [-67.5, 0, 0], "0.375": [-60, 0, 0], "0.4583": [-47.5, 0, 0], "0.75": [-120, 0, 0]}}, "waist": {"rotation": [-2.5, 0, 0], "position": [0, 1, 0]}, "left_leg": {"rotation": {"0.0": [10, 0, -20], "0.2083": [62.5, 0, -20], "0.2917": [50, 0, -20], "0.5833": [-55, 0, -20], "0.75": [10, 0, -20]}}, "right_leg": {"rotation": {"0.0": [10, 0, 20], "0.2917": [62.5, 0, 20], "0.375": [50, 0, 20], "0.6667": [-55, 0, 20], "0.75": [10, 0, 20]}}}}, "animation.feral.attack": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"root": {"position": [0, -2, 6]}, "spine": {"rotation": {"0.0": [92.5, 0, 0], "0.25": [27.5, 0, 0], "0.5": [92.5, 0, 0]}}, "head": {"rotation": {"0.0": [-80, 0, 0], "0.125": [-17.5, 0, 0], "0.375": [-80, 0, 0]}, "position": [0, 0, 0]}, "body": {"position": [0, 0, 0.25]}, "leftArm": {"rotation": {"0.0": [-106.47027, 24.09293, -6.8817], "0.125": [-67.5, 0, 0], "0.375": [-220.89339, 20.70481, 22.20765], "0.5": [-106.47027, 24.09293, -6.8817]}}, "rightArm": {"rotation": {"0.0": [-106.47027, -24.09293, 6.8817], "0.125": [-67.5, 0, 0], "0.375": [-220.89339, -20.70481, -22.20765], "0.5": [-106.47027, -24.09293, 6.8817]}}, "waist": {"rotation": [-2.5, 0, 0], "position": [0, 1, 0]}, "left_leg": {"rotation": {"0.0": [10, 0, -20], "0.25": [-17.5, 0, -20]}, "position": {"0.0": [0, 0, 0], "0.125": [0, 1, -1], "0.25": [0, 0, -1]}}, "right_leg": {"rotation": {"0.0": [10, 0, 20], "0.25": [25, 0, 20]}}}}, "animation.feral.vertical": {"loop": true, "bones": {"root": {"rotation": [-90, 0, 0]}}}, "animation.feral.death": {"loop": "hold_on_last_frame", "animation_length": 1.0417, "bones": {"root": {"position": [0, -1, 6]}, "spine": {"rotation": {"0.0": [92.5, 0, 0], "0.0833": [65, 0, 0], "0.2917": [65, 0, 0], "0.4167": [92.5, 0, 0], "0.5417": [92.5, 0, 0], "0.6667": [90, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.2917": [0, -9.5, -10], "0.4167": [0, -9.5, -10], "0.5417": [0, -8.75, -10], "0.6667": [0, -9.5, -10]}}, "head": {"rotation": {"0.0": [-80, 0, 0], "0.2083": [-95, 0, 0], "0.3333": [-95, 0, 0], "0.75": [180, 0, 0]}, "position": {"0.4583": [0, 0, 0], "0.6667": [0, 14, 8], "0.875": [0, 16, 1], "0.9583": [0, 18, 2], "1.0417": [0, 19, 1]}}, "body": {"position": [0, 0, 0.25]}, "leftArm": {"rotation": {"0.0": [-2.4905, -0.21782, -4.99527], "0.0417": [-8.38526, -0.21782, -4.99527], "0.0833": [-23.9611, -0.21782, -4.99527], "0.125": [-45.62156, -0.21782, -4.99527], "0.1667": [-69.35944, -0.21782, -4.99527], "0.2083": [-91.0199, -0.21782, -4.99527], "0.25": [-106.59574, -0.21782, -4.99527], "0.2917": [-112.4905, -0.21782, -4.99527], "0.3333": [-118.79005, -0.21782, -4.99527], "0.375": [-131.8037, -0.21782, -4.99527], "0.4167": [-147.91935, -0.21782, -4.99527], "0.4583": [-165.69011, -0.21782, -4.99527], "0.5": [-184.30351, -0.21782, -4.99527], "0.5417": [-203.17749, -0.21782, -4.99527], "0.5833": [-221.79089, -0.21782, -4.99527], "0.625": [-239.56164, -0.21782, -4.99527], "0.6667": [-255.6773, -0.21782, -4.99527], "0.7083": [-268.69095, -0.21782, -4.99527], "0.75": [-274.9905, -0.21782, -4.99527], "0.8333": [-277.4905, -0.21782, -4.99527], "0.9167": [-269.9905, -0.21782, -4.99527]}, "position": {"0.0": [0, -10.25, -9], "0.0417": [0.53589, -7.26206, -8.41052], "0.0833": [1.95187, -5.64581, -6.85294], "0.125": [3.92101, -4.60216, -4.68689], "0.1667": [6.07899, -3.91327, -2.31311], "0.2083": [8.04813, -3.48502, -0.14706], "0.25": [9.46411, -3.27101, 1.41052], "0.2917": [10, -3.25, 2], "0.3333": [10, -3.3219, 2.09045], "0.375": [10, -4.11252, 4.01797], "0.4167": [10, -4.99, 5.43], "0.4583": [10, -6.24364, 6.60651], "0.5": [10, -8.18691, 7.7885], "0.5417": [10, -10.49138, 8.92554], "0.5833": [10, -12.93269, 9.97451], "0.625": [10, -15.31532, 10.88854], "0.6667": [10, -17.42081, 11.60639], "0.7083": [10, -18.9339, 12.03395], "0.75": [10, -19.25, 12], "0.7917": [10, -18.24311, 11.70114], "0.8333": [10, -18.25, 12], "0.9167": [10, -19.5, 12]}}, "rightArm": {"rotation": {"0.0": [-2.4905, 0.21782, 4.99527], "0.25": [-137.65905, 7.26335, 2.41705], "0.5": [-87.65905, 7.26335, 2.41705]}, "position": {"0.0": [0, -10.5, -9], "0.1667": [0, -12.5, -14], "0.4167": [0, -19.5, -19]}}, "left_leg": {"rotation": {"0.0": [10, 0, -20], "0.2917": [90, 17.5, -20]}, "position": {"0.0": [5, 1, 1], "0.2917": [5, 3, 1]}}, "right_leg": {"rotation": {"0.0": [10, 0, 20], "0.2917": [90, -15, 20]}, "position": {"0.0": [-5, 1, 1], "0.2917": [-5, 3, 1]}}}}, "animation.feral.climb": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3))", "bones": {"root": {"position": [0, -1, 10]}, "spine": {"rotation": [90, 0, 0]}, "head": {"rotation": ["-90+math.sin((q.anim_time + 3) * 600) * 4", 0, 0], "position": [0, 0, "math.sin((q.anim_time + 4) * 600) * 0.5"]}, "body": {"rotation": ["math.sin(q.anim_time * 600) * 4", 0, 0], "position": [0, 0, "math.sin((q.anim_time + 3.4) * 600) * 0.3"]}, "leftArm": {"rotation": ["-99.9774+math.sin((q.anim_time + 7.1) * 300) * 48", 1.17339, -2.2077], "position": [0, "-0.25+math.clamp((math.sin((q.anim_time + 2) * 300) * 0.5), -0.3, 0.5)", "-2+math.clamp((math.sin((q.anim_time + 2) * 300) * 2), 0, 4)"]}, "rightArm": {"rotation": ["-99.9774-math.sin((q.anim_time + 7.1) * 300) * 48", 1.17339, -2.2077], "position": [0, "-0.25+math.clamp((-math.sin((q.anim_time + 2) * 300) * 0.5), -0.3, 0.5)", "-2+math.clamp((-math.sin((q.anim_time + 2) * 300) * 2), 0, 4)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 48", 1.17339, -2.2077], "position": [1, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-1.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 48", 1.17339, 2.20768], "position": [-1, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}}}