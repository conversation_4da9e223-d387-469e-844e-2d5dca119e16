{"format_version": "1.10.0", "animations": {"animation.evoker.walk": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"leg0": {"rotation": {"0.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-47.97, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-72.97, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-47.97, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-72.97, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}}}, "root": {"position": {"0.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [19, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [19, 0, 0], "lerp_mode": "catmullrom"}}}, "leftLeg": {"rotation": {"0.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}}}, "rightLeg": {"rotation": {"0.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}}}}}, "animation.evoker.riding_legs": {"loop": true, "bones": {"leg0": {"rotation": [-63.21677, 35.09641, 3.18643]}, "leg1": {"rotation": [-63.21677, -35.09641, -3.18643]}, "leftLeg": {"rotation": [-63.21677, -35.09641, -3.18643]}, "rightLeg": {"rotation": [-63.21677, 35.09641, 3.18643]}}}, "animation.evoker.cast": {"loop": "hold_on_last_frame", "animation_length": 2, "bones": {"spine": {"rotation": {"0.375": [0, 0, 0], "0.4583": [0, 2.5, 0], "0.5833": [0, -2.5, 0], "0.7083": [0, 2.5, 0], "0.8333": [0, -2.5, 0], "0.9583": [0, 2.5, 0], "1.0833": [0, -2.5, 0], "1.2083": [0, 2.5, 0], "1.3333": [0, -2.5, 0], "1.5": [0, 2.5, 0], "1.5833": [0, -2.5, 0], "1.7083": [0, 2.5, 0], "1.875": [0, -2.5, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.375": [0, -2, 0], "1.0": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.25": [40, 0, 0], "0.375": [30, 0, 0], "0.4583": [23.35318, -2.29543, -0.99073], "0.5833": [13.34371, 1.12085, 0.01083], "0.7083": [0, -2.5, 0], "0.8333": [0, 2.5, 0], "0.9583": [0, -2.5, 0], "1.0833": [0, 2.5, 0], "1.2083": [0, -2.5, 0], "1.3333": [0, 2.5, 0], "1.5": [0, -2.5, 0], "1.5833": [0, 2.5, 0], "1.7083": [0, -2.5, 0], "1.875": [0, 2.5, 0], "2.0": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.0417": [-7.5967, 0.84334, 1.22996], "0.0833": [-18.05677, 2.27547, 2.8276], "0.125": [-27.9184, 3.77249, 3.67304], "0.1667": [-36.33323, 4.89111, 3.41015], "0.2083": [-44.37697, 5.3283, 1.59605], "0.25": [-56.90388, 4.13529, -3.79762], "0.2917": [-80.03478, -6.23708, -18.8466], "0.3333": [-95.06213, -40.76062, -48.98256], "0.375": [-40.00672, -52.20054, -118.19041], "0.4167": [-14.98204, -32.1316, -142.33916], "0.4583": [-6.72419, -15.07486, -143.54003], "0.5": [-5.57218, -10.67345, -136.38574], "0.5417": [-9.73535, -16.04337, -129.13216], "0.5833": [-24.23889, -31.86856, -117.52534], "0.625": [-49.87225, -43.63555, -92.87929], "0.6667": [-70.64251, -44.69491, -71.66218], "0.7083": [-85.54107, -43.217, -57.78145], "0.75": [-100.0091, -40.06384, -45.43761], "0.7917": [-108.92318, -38.30979, -39.2905], "0.8333": [-99.3861, -52.89058, -57.17299], "0.875": [-26.25151, -44.63673, -132.22492], "0.9167": [-11.6714, -21.86201, -135.64513], "0.9583": [-15.13423, -23.34746, -125.94195], "1.0": [-39.93613, -40.81886, -102.78373], "1.0417": [-70.69673, -44.91187, -71.87982], "1.0833": [-92.91252, -42.2707, -51.76876], "1.125": [-109.16232, -38.53848, -39.3443], "1.1667": [-103.31956, -51.55426, -53.22921], "1.2083": [-34.13348, -50.64722, -125.47165], "1.25": [-13.44711, -24.87232, -135.16463], "1.2917": [-15.06372, -23.40077, -126.30992], "1.3333": [-39.59035, -40.8525, -103.37552], "1.375": [-70.69673, -44.91187, -71.87982], "1.4167": [-92.91252, -42.2707, -51.76876], "1.4583": [-109.16232, -38.53848, -39.3443], "1.5": [-103.31956, -51.55426, -53.22921], "1.5417": [-34.13348, -50.64722, -125.47165], "1.5833": [-13.44711, -24.87232, -135.16463], "1.625": [-15.06372, -23.40077, -126.30992], "1.6667": [-39.59035, -40.8525, -103.37552], "1.7083": [-70.69673, -44.91187, -71.87982], "1.75": [-92.91252, -42.2707, -51.76876], "1.7917": [-109.16232, -38.53848, -39.3443], "2.0": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.0417": [-7.29873, -0.81553, -1.31296], "0.0833": [-17.33628, -2.1881, -2.85886], "0.125": [-26.95224, -3.64281, -3.69693], "0.1667": [-35.35743, -4.76782, -3.46192], "0.2083": [-43.60172, -5.27379, -1.71512], "0.25": [-56.07467, -4.04157, 3.83996], "0.2917": [-78.07046, 6.37322, 19.27735], "0.3333": [-93.10701, 38.39111, 47.92264], "0.375": [-55.65792, 55.27916, 100.96142], "0.4167": [-34.13348, 50.64722, 125.47165], "0.4583": [-91.17684, 56.39941, 66.84592], "0.5": [-109.16232, 38.53848, 39.3443], "0.5417": [-92.91252, 42.2707, 51.76876], "0.5833": [-70.69673, 44.91187, 71.87982], "0.625": [-39.59035, 40.8525, 103.37552], "0.6667": [-15.06372, 23.40077, 126.30992], "0.7083": [-13.44711, 24.87232, 135.16463], "0.75": [-34.13348, 50.64722, 125.47165], "0.7917": [-103.31956, 51.55426, 53.22921], "0.8333": [-109.16232, 38.53848, 39.3443], "0.875": [-92.91252, 42.2707, 51.76876], "0.9167": [-70.69673, 44.91187, 71.87982], "0.9583": [-39.59035, 40.8525, 103.37552], "1.0": [-15.06372, 23.40077, 126.30992], "1.0417": [-13.44711, 24.87232, 135.16463], "1.0833": [-34.13348, 50.64722, 125.47165], "1.125": [-103.31956, 51.55426, 53.22921], "1.1667": [-109.16232, 38.53848, 39.3443], "1.2083": [-92.91252, 42.2707, 51.76876], "1.25": [-70.69673, 44.91187, 71.87982], "1.2917": [-39.84998, 40.96921, 103.14758], "1.3333": [-15.06372, 23.40077, 126.30992], "1.375": [-11.5995, 21.8875, 135.99577], "1.4167": [-26.06115, 44.66511, 132.62966], "1.4583": [-99.47492, 53.14969, 57.34108], "1.5": [-109.16232, 38.53848, 39.3443], "1.5417": [-100.21736, 40.2903, 45.51572], "1.5833": [-85.67838, 43.44326, 57.92057], "1.625": [-70.69673, 44.91187, 71.87982], "1.6667": [-49.81639, 43.81592, 93.20976], "1.7083": [-24.14861, 31.96007, 117.90292], "1.75": [-9.68732, 16.07658, 129.48988], "1.7917": [-6.77474, 12.63571, 135.16266], "2.0": [0, 0, 0]}}, "root": {"position": {"0.25": [0, 0, 0], "0.4167": [0, -1, 0], "1.5417": [0, -1, 0], "1.7083": [0, 0, 0]}}, "leftLeg": {"rotation": {"0.1667": [0, 0, 0], "0.3333": [0, 0, -12.5], "1.5": [0, 0, -12.5], "1.6667": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [1, 2, 0], "0.375": [1, 0, 0], "1.4583": [1, 0, 0], "1.5833": [1, 2, 0], "1.8333": [0, 0, 0]}}, "rightLeg": {"rotation": {"0.375": [0, 0, 0], "0.5417": [0, 0, 12.5], "1.6667": [0, 0, 12.5], "1.8333": [0, 0, 0]}, "position": {"0.2083": [0, 0, 0], "0.4583": [-1, 2, 0], "0.5833": [-1, 0, 0], "1.625": [-1, 0, 0], "1.75": [-1, 2, 0], "2.0": [0, 0, 0]}}}}, "animation.evoker.celebrate": {"loop": true, "animation_length": 2, "anim_time_update": "q.anim_time+q.delta_time*1.5", "bones": {"root": {"position": {"0.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.875": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.875": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leftArm": {"rotation": {"0.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "rightArm": {"rotation": {"0.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leftLeg": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}, "rightLeg": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}}}, "animation.evoker.death": {"loop": "hold_on_last_frame", "animation_length": 1, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-25, 0, 0], "0.5": [-90, 0, 0], "0.5833": [-87.5, 0, 0], "0.6667": [-90, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [22.5, 0, 0], "0.4167": [17.5, 0, 0], "0.5": [22.5, 0, 0], "0.5417": [4.69, 0, 0], "0.5833": [22.5, 0, 0], "0.9167": [0, 0, 0]}}, "nose": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-17.5, 0, 0], "0.4583": [-17.5, 0, 0], "0.5": [0, 0, 0], "0.5417": [-17.5, 0, 0], "0.5833": [0, 0, 0]}}, "arms": {"rotation": {"0.0": [-57.5, 0, 0], "0.1667": [-77.5, 0, 0], "0.5": [-82.5, 0, 0], "0.5417": [-67.19, 0, 0], "0.5833": [-82.5, 0, 0], "0.8333": [-40, 0, 0]}}, "rightLeg": {"rotation": {"0.0": [0, 0, 0], "0.125": [-67.5, 0, 0], "0.5": [-10, 0, 0], "0.5833": [0, 0, 0], "0.6667": [-5, 0, 0], "0.75": [0, 0, 0]}}}}}}