{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:pyro_charger_fireball", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:snowball"}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {}}}, "events": {"rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0, "height": 0}, "minecraft:scale": {"value": 1e-05}, "minecraft:type_family": {"family": ["pyro_charger_fireball", "projectile"]}, "minecraft:projectile": {"power": 1, "gravity": 0.005, "inertia": 0.95, "liquid_inertia": 0.5, "anchor": 2, "offset": [0, 0.5, 1], "semi_random_diff_damage": false, "uncertainty_base": 5, "uncertainty_multiplier": 3, "reflect_on_hurt": false}, "minecraft:timer": {"time": 4, "looping": true, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:physics": {"has_collision": false}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 96, "max_dropped_ticks": 1, "use_motion_prediction_hints": true}}}}}