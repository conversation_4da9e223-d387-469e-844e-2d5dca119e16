export function calculateDistance(loc1, loc2) {
    const dx = loc1.x - loc2.x;
    const dy = loc1.y - loc2.y;
    const dz = loc1.z - loc2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}
export function fixedPosRaycast(from, to, distance, step) {
    const positions = [];
    for (let i = 0; i <= distance; i += step) {
        const t = i / distance;
        positions.push({
            x: from.x + t * (to.x - from.x),
            y: from.y + t * (to.y - from.y),
            z: from.z + t * (to.z - from.z),
        });
    }
    return positions;
}
export function fixedLenRaycast(from, direction, length, step) {
    const positions = [];
    for (let i = 0; i <= length; i += step) {
        positions.push({
            x: from.x + direction.x * i,
            y: from.y + direction.y * i,
            z: from.z + direction.z * i,
        });
    }
    return positions;
}
