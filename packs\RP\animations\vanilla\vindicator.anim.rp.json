{"format_version": "1.10.0", "animations": {"animation.vindicator.hide_item": {"loop": true, "bones": {"rightItem": {"scale": 0}}}, "animation.vindicator.hide_hands": {"loop": true, "bones": {"leftArm": {"scale": 0}, "rightArm": {"scale": 0}}}, "animation.vindicator.hide_arms": {"loop": true, "bones": {"arms": {"scale": 0}}}, "animation.vindicator.show_hands_attacking": {"loop": true, "bones": {"leftArm": {"scale": 1}, "rightArm": {"scale": 1}, "arms": {"scale": 0}, "rightItem": {"scale": 1}}}, "animation.vindicator.idle": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"head": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-67.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-67.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-67.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": [-7.5, 0, 5], "position": [0, -0.5, 0]}, "leg1": {"rotation": [0, 0, -5]}, "rightArm": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leftArm": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "0.7083": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.vindicator.walk": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "rightArm": {"rotation": {"0.0": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-130, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-135, 0, 0], "lerp_mode": "catmullrom"}}}, "leftArm": {"rotation": {"0.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.vindicator.walk_drinking": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [7.5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.25, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [-60, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-65, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [19.18, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.79, 0.9], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 2], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 3, 1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "rightArm": {"rotation": [-69.4851, -36.78095, 1.29045]}, "leftArm": {"rotation": {"0.0": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [8.19, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-3.75, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-30, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-5.96, 0, 0], "lerp_mode": "catmullrom"}, "1.2917": {"post": [11.79, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [30, 0, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0.5, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.vindicator.riding_arms": {"loop": true, "bones": {"leftArm": {"rotation": [-53.58411, 22.00437, -6.48664]}, "rightArm": {"rotation": [-53.58411, -22.00437, 6.48664]}}}, "animation.vindicator.riding_legs": {"loop": true, "bones": {"leftLeg": {"rotation": [-63.21677, -35.09641, -3.18643]}, "rightLeg": {"rotation": [-63.21677, 35.09641, 3.18643]}, "leg0": {"rotation": [-63.21677, 35.09641, 3.18643]}, "leg1": {"rotation": [-63.21677, -35.09641, -3.18643]}}}, "animation.vindicator.drink": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3.5))", "bones": {"rightArm": {"rotation": [-69.4851, -36.78095, 1.29045]}}}, "animation.vindicator.attack": {"loop": "hold_on_last_frame", "animation_length": 0.75, "anim_time_update": "q.anim_time+q.delta_time*1.6", "bones": {"spine": {"rotation": {"0.0": [0, 0, 0], "0.125": [-18.74538, 24.3683, -5.7632], "0.5": [15.50409, -14.47751, -3.96713]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.25": [-17.5, 0, 0], "0.4167": [-17.5, 0, 0], "0.5833": [0, 0, 0]}}, "nose": {"rotation": {"0.0": [0, 0, 0], "0.2083": [10, 0, 0], "0.375": [-12.5, 0, 0], "0.5": [-12.5, 0, 0], "0.6667": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.3333": [12.5, 0, -35], "0.5833": [-24.66569, 4.20854, 9.07947]}}, "rightArm": {"rotation": {"0.0": [-17.5, 0, 0], "0.2083": [-99.47028, -14.36495, -23.70348], "0.5": [84.2633, -15.18892, -13.16783]}, "position": {"0.0": [0, 0, 0], "0.125": [0, 1, 2], "0.5417": [0, 0, -2]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.2917": [-35, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.2083": [0, 2, 0], "0.4167": [0, -1.5, 0]}}, "leg1": {"rotation": {"0.2917": [0, 0, 0], "0.6667": [0, 0, -12.5]}, "position": {"0.2917": [0, 0, 0], "0.5": [1, 1, 0], "0.6667": [1, -0.5, 0]}}}}, "animation.vindicator.celebrate": {"loop": true, "animation_length": 2, "bones": {"root": {"position": {"0.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1.44, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leftArm": {"rotation": {"0.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, -137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "rightArm": {"rotation": {"0.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 87.5], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 137.5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, 10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}}}}}, "animation.vindicator.death": {"loop": "hold_on_last_frame", "animation_length": 1.875, "anim_time_update": "q.anim_time+q.delta_time*2.8", "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "1.0": [-90, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, 2, 1]}}, "spine": {"rotation": {"0.0": [0, 0, 0], "0.5": [32.5, 0, 0], "1.0": [32.5, 0, 0], "1.25": [0, 0, 0], "1.4583": [5, 0, 0], "1.7917": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.4167": [15, 0, 0], "1.125": [15, 0, 0], "1.375": [1.29298, -37.33289, -3.82607], "1.5833": [5.4231, -41.05764, -4.40357], "1.7917": [-0.44677, -44.78238, -4.98107]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.4167": [-100.08453, -7.38542, 1.30962], "1.0": [-65.43798, -22.9824, -10.11783], "1.4167": [0, 0, -47.5], "1.625": [-5, 0, -47.5], "1.875": [0, 0, -47.5]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.4167": [-100.08453, 7.38542, -1.30962], "1.0": [-65.43798, 22.9824, 10.11783], "1.4167": [0, 0, 15], "1.625": [-4.33287, -2.49762, 17.5945], "1.875": [0, 0, 15]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.2917": [23.30696, -9.30727, 20.57639], "0.5833": [-14.19304, -9.30727, 20.57639], "1.0": [-14.19304, -9.30727, 20.57639], "1.2083": [3.30696, -9.30727, 20.57639], "1.4167": [-1.69304, -9.30727, 20.57639], "1.625": [3.30696, -9.30727, 20.57639]}, "position": {"0.0": [0, 0, 0], "0.2917": [0, 3, -1], "0.5833": [0, 0, 0]}}, "arms": {"rotation": {"0.0": [-60, 0, 0], "0.5": [-65, 0, 0], "1.3333": [-65, 0, 0], "1.5": [-47.5, 0, 0], "1.625": [-52.5, 0, 0], "1.75": [-47.5, 0, 0]}}}}}}