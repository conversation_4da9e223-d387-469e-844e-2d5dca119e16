{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "rza:witherator_exhaust", "basic_render_parameters": {"material": "particles_alpha", "texture": "textures/particle/particles"}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": "math.random_integer(10,150)"}, "minecraft:emitter_lifetime_once": {"active_time": 1}, "minecraft:emitter_shape_point": {"offset": [0, -1, 0], "direction": ["math.random(-0.5,0.5)", "math.random(4,16)", "math.random(-0.5,0.5)"]}, "minecraft:particle_lifetime_expression": {"max_lifetime": "4/math.random(1,5)+0.1"}, "minecraft:particle_initial_speed": "math.random(20, 30)", "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, "math.random(1, 4)", 0], "linear_drag_coefficient": "math.random(1, 6)"}, "minecraft:particle_appearance_billboard": {"size": ["v.particle_age<=0.3?0.1+v.particle_age*0.08:0.2+v.particle_age*-0.05", "v.particle_age<=0.3?0.1+v.particle_age*0.08:0.2+v.particle_age*-0.05"], "facing_camera_mode": "rotate_xyz", "uv": {"texture_width": 128, "texture_height": 128, "flipbook": {"base_UV": [56, 0], "size_UV": [8, 8], "step_UV": [-8, 0], "frames_per_second": 8, "max_frame": 8, "stretch_to_lifetime": true}}}, "minecraft:particle_appearance_lighting": {}, "minecraft:particle_appearance_tinting": {"color": ["variable.particle_random_1 * 0.3 + 0.7", "variable.particle_random_1 * 0.3 + 0.7", "variable.particle_random_1 * 0.3 + 0.7", 1]}}}}