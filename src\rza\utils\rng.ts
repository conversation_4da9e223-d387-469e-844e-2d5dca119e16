// RANDOM NUMBER GENERATOR UTILITIES

// Store generated numbers to ensure uniqueness in a range
let generatedNumbers: number[] = [];

/**
 * Generates a random integer between min and max (inclusive)
 * Returns unique numbers until range is exhausted
 * @param min The minimum value (inclusive)
 * @param max The maximum value (inclusive)
 * @returns A random integer between min and max
 * @throws Error if min is greater than max
 */
export function getRandomInt(min: number, max: number): number {
  /**
   * Checks if all possible numbers in a range have been used
   * @returns True if all numbers in range have been used
   */
  function isRangeExhausted(): boolean {
    return generatedNumbers.length >= max - min + 1;
  }

  // Ensure parameters are integers
  min = Math.ceil(min);
  max = Math.floor(max);

  // Validate input
  if (min > max) {
    console.warn(`getRandomInt: min (${min}) is greater than max (${max})`);
  }

  // Clear stored numbers if range is exhausted
  if (isRangeExhausted()) {
    generatedNumbers.length = 0;
  }

  let randomNum: number;
  do {
    randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
  } while (generatedNumbers.includes(randomNum));

  generatedNumbers.push(randomNum);

  return randomNum;
}
/**
 * Generates a random float between min and max with configurable decimal places
 * @param min The minimum value (inclusive)
 * @param max The maximum value (inclusive)
 * @param decimals Number of decimal places to round to (default 2)
 * @returns A random float between min and max with specified decimal places
 * @throws Warning if min is greater than max
 */
export function getRandomFloat(
  min: number,
  max: number,
  decimals: number,
): number {
  // Validate input
  if (min > max) {
    console.warn(`getRandomFloat: min (${min}) is greater than max (${max})`);
  }

  // Generate random float and round to specified decimals
  const randomFloat = Math.random() * (max - min) + min;
  return Number(randomFloat.toFixed(decimals));
}
