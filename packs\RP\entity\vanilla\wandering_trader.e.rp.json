{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:wandering_trader", "min_engine_version": "1.16.0", "materials": {"default": "wandering_trader"}, "textures": {"default": "textures/entity/wandering_trader"}, "geometry": {"default": "geometry.villager_v2.rza"}, "animations": {"look_at_target": "animation.common.look_at_target", "blink": "animation.general_front.blink", "idle": "animation.villager.idle", "walk": "animation.villager.walk", "run": "animation.villager.run", "raise_arms": "animation.villager.raise_arms", "get_in_bed": "animation.villager.get_in_bed", "throw": "animation.villager.throw", "death_rot": "animation.general.death_rot", "death": "animation.villager.death", "arms_controller": "controller.animation.villager.raise_arms", "throw_controller": "controller.animation.villager.throw", "death_controller": "controller.animation.villager.death", "general": "controller.animation.villager.general"}, "scripts": {"scale": "0.9375", "initialize": ["v.blink_speed = math.random(0.6, 3);"], "animate": ["death_controller"]}, "render_controllers": ["controller.render.wandering_trader"], "spawn_egg": {"texture": "spawn_egg_wandering_trader"}}}}