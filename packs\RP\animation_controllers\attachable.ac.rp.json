{"format_version": "1.10.0", "animation_controllers": {"controller.animation.attachable.general": {"initial_state": "first_person", "states": {"first_person": {"animations": ["first_person_hold"], "transitions": [{"third_person": "!c.is_first_person"}]}, "third_person": {"animations": ["third_person_hold"], "transitions": [{"first_person": "c.is_first_person"}]}}}, "controller.animation.attachable.fire": {"initial_state": "default", "states": {"default": {"transitions": [{"fire": "q.is_using_item"}]}, "fire": {"animations": ["fire"], "transitions": [{"default": "q.all_animations_finished"}]}}}}}