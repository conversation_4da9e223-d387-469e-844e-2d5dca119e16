import { EntityDamageCause } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";
function rotationToDirection(rotation) {
    const yawRad = (rotation.y * Math.PI) / 180;
    const pitchRad = (rotation.x * Math.PI) / 180;
    const direction = { x: -Math.sin(yawRad) * Math.cos(pitchRad), y: -Math.sin(pitchRad), z: Math.cos(yawRad) * Math.cos(pitchRad) };
    return direction;
}
export function fireLaserPulse(laserTurret) {
    const dimension = laserTurret.dimension;
    const location = laserTurret.location;
    const rotation = laserTurret.getRotation();
    const direction = rotationToDirection(rotation);
    const startOffset = 2;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.65 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };
    const positions = fixedLenRaycast(startPos, direction, 32, 0.5);
    for (const pos of positions) {
        try {
            dimension.spawnParticle("rza:purple_laser", pos);
            dimension.spawnParticle("rza:purple_laser_outward", pos);
            dimension.getEntities({ location: pos, families: ["zombie"], maxDistance: 3 }).forEach((zombie) => {
                zombie.applyDamage(7, { cause: EntityDamageCause.contact, damagingEntity: laserTurret });
                zombie.setOnFire(3, true);
            });
        }
        catch (e) { }
    }
    dimension.playSound("turret.laser_turret.fire", location, { volume: 8 });
    return;
}
