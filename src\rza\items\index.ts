import { world } from "@minecraft/server";
import { ItemComponentRegistry } from "@minecraft/server";
import { itemFeatures } from "./items";

// Register custom component and events
export function itemComponents(registry: ItemComponentRegistry) {
  registry.registerCustomComponent("rza:item", {
    onUse: (data) => {
      const item = data.itemStack;
      const player = data.source;
      if (!item) return;

      const itemTypeId = item.type.id;
      itemFeatures(item, itemTypeId, player);
    },
  });
  return;
}

// Subscribe to item use events for continuous use handling
world.beforeEvents.itemUse.subscribe((data) => {
  const item = data.itemStack;
  const player = data.source;
  if (!item) return;

  itemFeatures(item, item.typeId, player, true);
});

world.afterEvents.itemStopUse.subscribe((data) => {
  const item = data.itemStack;
  const player = data.source;
  if (!item) return;

  itemFeatures(item, item.typeId, player, false);
});
