{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "minecraft:ravager", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:celebrate": {"minecraft:behavior.celebrate": {"priority": 5, "celebration_sound": "celebrate", "sound_interval": {"range_min": 2, "range_max": 7}, "jump_interval": {"range_min": 1, "range_max": 3.5}, "duration": 30, "on_celebration_end_event": {"event": "minecraft:stop_celebrating", "target": "self"}}}, "minecraft:pillager_rider": {"minecraft:addrider": {"entity_type": "minecraft:pillager"}}, "minecraft:pillager_rider_for_raid": {"minecraft:addrider": {"entity_type": "minecraft:pillager", "spawn_event": "minecraft:spawn_for_raid"}}, "minecraft:evoker_rider_for_raid": {"minecraft:addrider": {"entity_type": "minecraft:evocation_illager", "spawn_event": "minecraft:spawn_for_raid"}}, "minecraft:pillager_captain_rider": {"minecraft:addrider": {"entity_type": "minecraft:pillager", "spawn_event": "minecraft:spawn_as_illager_captain"}}, "minecraft:vindicator_rider": {"minecraft:addrider": {"entity_type": "minecraft:vindicator"}}, "minecraft:vindicator_captain_rider": {"minecraft:addrider": {"entity_type": "minecraft:vindicator", "spawn_event": "minecraft:spawn_as_illager_captain"}}, "minecraft:raid_configuration": {"minecraft:dweller": {"dwelling_type": "village", "dweller_role": "defender", "update_interval_base": 60, "update_interval_variant": 40, "can_find_poi": false, "can_migrate": true, "first_founding_reward": 0}, "minecraft:behavior.move_to_village": {"priority": 5, "speed_multiplier": 1, "goal_radius": 2}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1}, "minecraft:ambient_sound_interval": {"value": 4, "range": 8, "event_name": "ambient.in.raid"}}, "minecraft:raid_persistence": {"minecraft:persistent": {}}, "minecraft:hostile": {"minecraft:movement": {"value": 0.4}, "minecraft:behavior.delayed_attack": {"priority": 4, "attack_once": false, "track_target": false, "require_complete_path": false, "random_stop_interval": 0, "reach_multiplier": 1.5, "speed_multiplier": 1, "attack_duration": 0.75, "hit_delay_pct": 0.5, "on_attack": {"event": "rza:damage_zombies", "target": "self"}}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.4}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6, "angle_of_view_horizontal": 45, "probability": 1}, "minecraft:behavior.look_at_entity": {"priority": 10, "look_distance": 8, "angle_of_view_horizontal": 45, "filters": {"test": "is_family", "subject": "other", "value": "mob"}}, "minecraft:behavior.hurt_by_target": {"priority": 5, "entity_types": {"must_see": true, "must_see_forget_duration": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "must_reach": true, "persist_time": 0, "reselect_targets": true, "within_radius": 12, "reevaluate_description": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 12}]}, "minecraft:behavior.mount_pathing": {"priority": 5, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true}, "minecraft:environment_sensor": {"triggers": [{"filters": {"all_of": [{"test": "actor_health", "subject": "self", "operator": "<=", "value": 20}, {"test": "has_target", "subject": "self", "value": true}, {"test": "target_distance", "subject": "other", "operator": "<=", "value": 4}]}, "event": "minecraft:start_roar", "target": "self"}]}}, "stunned": {"minecraft:is_stunned": {}, "minecraft:timer": {"looping": false, "time": 2, "time_down_event": {"event": "minecraft:start_roar"}}}, "roaring": {"minecraft:behavior.knockback_roar": {"priority": 1, "duration": 1, "attack_time": 0.5, "knockback_damage": 6, "knockback_horizontal_strength": 3, "knockback_vertical_strength": 3, "knockback_range": 4, "knockback_filters": {"test": "is_family", "subject": "other", "value": "zombie"}, "damage_filters": {"test": "is_family", "subject": "other", "value": "zombie"}, "cooldown_time": 2}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "actor_health", "subject": "self", "operator": ">=", "value": 20}, "event": "minecraft:end_roar", "target": "self"}, {"filters": {"any_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "target_distance", "subject": "other", "operator": ">=", "value": 4}]}, "event": "minecraft:end_roar", "target": "self"}]}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["minecraft:hostile"]}}, "rza:spawn_for_village": {"sequence": [{"add": {"component_groups": ["minecraft:hostile"]}}, {"randomize": [{"weight": 10, "add": {"component_groups": ["minecraft:evoker_rider_for_raid"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:pillager_captain_rider"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:vindicator_captain_rider"]}}]}]}, "rza:damage_zombies": {"queue_command": {"command": "damage @e[family=zombie, r=4] 4 entity_attack entity @s"}}, "minecraft:spawn_for_raid": {"add": {"component_groups": ["minecraft:hostile", "minecraft:raid_configuration", "minecraft:raid_persistence"]}}, "minecraft:spawn_for_raid_with_evoker_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:evoker_rider_for_raid", "minecraft:raid_configuration", "minecraft:raid_persistence"]}}, "minecraft:spawn_for_raid_with_pillager_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:pillager_rider_for_raid", "minecraft:raid_configuration", "minecraft:raid_persistence"]}}, "minecraft:spawn_with_pillager_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:pillager_rider"]}}, "minecraft:spawn_with_pillager_captain_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:pillager_captain_rider"]}}, "minecraft:spawn_with_vindicator_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:vindicator_rider"]}}, "minecraft:spawn_with_vindicator_captain_rider": {"add": {"component_groups": ["minecraft:hostile", "minecraft:vindicator_captain_rider"]}}, "minecraft:become_stunned": {"add": {"component_groups": ["stunned"]}, "remove": {"component_groups": ["minecraft:hostile"]}}, "minecraft:start_roar": {"add": {"component_groups": ["roaring"]}, "remove": {"component_groups": ["stunned"]}}, "minecraft:end_roar": {"add": {"component_groups": ["minecraft:hostile"]}, "remove": {"component_groups": ["roaring"]}}, "minecraft:start_celebrating": {"sequence": [{"add": {"component_groups": ["minecraft:celebrate"]}}, {"filters": {"test": "has_nametag", "value": false}, "remove": {"component_groups": ["minecraft:raid_persistence"]}}]}, "minecraft:stop_celebrating": {"remove": {"component_groups": ["minecraft:celebrate"]}}, "minecraft:raid_expired": {"filters": {"test": "has_nametag", "value": false}, "remove": {"component_groups": ["minecraft:raid_persistence"]}}, "rza:heal": {"queue_command": {"command": "effect @s regeneration 2 3"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player?20:0"}, "minecraft:behavior.float": {"priority": 0}, "minecraft:ravager_blocked": {"knockback_strength": 3, "reaction_choices": [{"weight": 1, "value": {"event": "minecraft:become_stunned", "target": "self"}}, {"weight": 1}]}, "minecraft:attack": {"damage": 12}, "minecraft:breathable": {"suffocate_time": 0, "total_supply": 15}, "minecraft:collision_box": {"height": 2.2, "width": 1.95}, "minecraft:health": {"max": 100, "value": 100}, "minecraft:damage_sensor": {"triggers": {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:jump.static": {}, "minecraft:loot": {"table": "loot_tables/entities/ravager.json"}, "minecraft:knockback_resistance": {"value": 0.75}, "minecraft:movement": {"value": 0}, "minecraft:movement.basic": {}, "minecraft:nameable": {}, "minecraft:navigation.walk": {"avoid_damage_blocks": true, "can_path_over_water": true, "avoid_water": true, "can_sink": false}, "minecraft:preferred_path": {"max_fall_blocks": 1, "jump_cost": 5, "default_block_cost": 1.5, "preferred_path_blocks": [{"cost": 0, "blocks": ["grass_path"]}, {"cost": 1, "blocks": ["cobblestone", "stone", "granite", "polished_granite", "diorite", "polished_diorite", "andesite", "polished_andesite", "stone_bricks", "mossy_stone_bricks", "cracked_stone_bricks", "chiseled_stone_bricks", "sandstone", "cut_sandstone", "chiseled_sandstone", "smooth_sandstone", "mossy_cobblestone", "smooth_stone_slab", "sandstone_slab", "cobblestone_slab", "brick_slab", "stone_brick_slab", "quartz_slab", "nether_brick_slab", "red_sandstone_slab", "purpur_slab", "prismarine_slab", "dark_prismarine_slab", "prismarine_brick_slab", "mossy_cobblestone_slab", "smooth_sandstone_slab", "red_nether_brick_slab", "end_stone_brick_slab", "smooth_red_sandstone_slab", "polished_andesite_slab", "andesite_slab", "diorite_slab", "polished_diorite_slab", "granite_slab", "polished_granite_slab", "mossy_stone_brick_slab", "smooth_quartz_slab", "normal_stone_slab", "cut_sandstone_slab", "cut_red_sandstone_slab", "smooth_stone_double_slab", "sandstone_double_slab", "cobblestone_double_slab", "brick_double_slab", "stone_brick_double_slab", "quartz_double_slab", "nether_brick_double_slab", "red_sandstone_double_slab", "purpur_double_slab", "prismarine_double_slab", "dark_prismarine_double_slab", "prismarine_brick_double_slab", "mossy_cobblestone_double_slab", "smooth_sandstone_double_slab", "red_nether_brick_double_slab", "end_stone_brick_double_slab", "smooth_red_sandstone_double_slab", "polished_andesite_double_slab", "andesite_double_slab", "diorite_double_slab", "polished_diorite_double_slab", "granite_double_slab", "polished_granite_double_slab", "mossy_stone_brick_double_slab", "smooth_quartz_double_slab", "normal_stone_double_slab", "cut_sandstone_double_slab", "cut_red_sandstone_double_slab", "oak_slab", "spruce_slab", "birch_slab", "jungle_slab", "acacia_slab", "dark_oak_slab", "oak_double_slab", "spruce_double_slab", "birch_double_slab", "jungle_double_slab", "acacia_double_slab", "dark_oak_double_slab", "oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "brick_block", "nether_brick", "red_nether_brick", "end_bricks", "red_sandstone", "cut_red_sandstone", "chiseled_red_sandstone", "smooth_red_sandstone", "white_stained_glass", "orange_stained_glass", "magenta_stained_glass", "light_blue_stained_glass", "yellow_stained_glass", "lime_stained_glass", "pink_stained_glass", "gray_stained_glass", "light_gray_stained_glass", "cyan_stained_glass", "purple_stained_glass", "blue_stained_glass", "brown_stained_glass", "green_stained_glass", "red_stained_glass", "black_stained_glass", "glass", "glowstone", "prismarine", "emerald_block", "diamond_block", "lapis_block", "gold_block", "redstone_block", "purple_glazed_terracotta", "white_glazed_terracotta", "orange_glazed_terracotta", "magenta_glazed_terracotta", "light_blue_glazed_terracotta", "yellow_glazed_terracotta", "lime_glazed_terracotta", "pink_glazed_terracotta", "gray_glazed_terracotta", "silver_glazed_terracotta", "cyan_glazed_terracotta", "blue_glazed_terracotta", "brown_glazed_terracotta", "green_glazed_terracotta", "red_glazed_terracotta", "black_glazed_terracotta"]}, {"cost": 50, "blocks": ["bed", "lectern", "composter", "grindstone", "blast_furnace", "smoker", "fletching_table", "cartography_table", "brewing_stand", "smithing_table", "cauldron", "barrel", "loom", "stonecutter"]}]}, "minecraft:behavior.move_to_land": {"priority": 2, "goal_radius": 0.5, "speed_multiplier": 1.1}, "minecraft:behavior.defend_village_target": {"priority": 1, "attack_chance": 0.25, "entity_types": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "value": "player"}]}}}, "minecraft:behavior.move_through_village": {"priority": 3, "speed_multiplier": 0.6, "only_at_night": true}, "minecraft:behavior.move_towards_dwelling_restriction": {"priority": 1, "speed_multiplier": 1}, "minecraft:dweller": {"dwelling_type": "village", "dweller_role": "defender", "update_interval_base": 60, "update_interval_variant": 40, "can_find_poi": false, "can_migrate": true, "first_founding_reward": 5}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:follow_range": {"value": 64}, "minecraft:rideable": {"seat_count": 1, "family_types": ["pillager", "vindicator", "evocation_illager"], "seats": {"position": [0, 2.1, -0.3]}}, "minecraft:type_family": {"family": ["illager", "ravager", "mob"]}}}}