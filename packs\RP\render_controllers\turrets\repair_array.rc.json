{"format_version": "1.10.0", "render_controllers": {"controller.render.repair_array": {"geometry": "geometry.default", "materials": [{"*": "material.default"}, {"shaft": "material.shaft"}], "textures": ["(query.property('rza:active') == true && query.property('rza:fire') == true) ? texture.active_firing : query.property('rza:active') == true ? texture.active : texture.inactive"], "is_hurt_color": {"r": 1, "g": 1, "b": 1, "a": "q.is_alive?0.3:0.1"}}, "controller.render.repair_array_item": {"geometry": "geometry.default", "materials": [{"*": "material.default"}, {"shaft": "material.shaft"}], "textures": ["texture.default"], "is_hurt_color": {"r": 1, "g": 1, "b": 1, "a": "q.is_alive?0.3:0.1"}}}}