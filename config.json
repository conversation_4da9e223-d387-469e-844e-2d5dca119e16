{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "<PERSON><PERSON>'s Zombie Apocalypse", "packs": {"behaviorPack": "./packs/BP", "resourcePack": "./packs/RP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"addon_builder": {"url": "github.com/Raboy-13/Regolith-Filters", "version": "1782a334249949b100788361993cc62a7a8c36b0"}}, "formatVersion": "1.4.0", "profiles": {"build": {"export": {"readOnly": false, "target": "local"}, "filters": [{"filter": "addon_builder"}]}, "default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}}}}