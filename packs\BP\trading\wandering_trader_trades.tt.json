{"tiers": [{"groups": [{"num_to_select": 8, "trades": [{"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:bucket", "quantity": 1, "functions": [{"function": "set_data", "data": {"min": 0, "max": 5}}]}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:packed_ice", "quantity": 1}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 6}], "gives": [{"item": "minecraft:blue_ice", "quantity": 1}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:podzol", "quantity": 3}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:gunpowder", "quantity": 6}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:tnt", "quantity": 4}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:sea_pickle", "quantity": 1}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:slime_ball", "quantity": 1}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:glowstone", "quantity": 1}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:nautilus_shell", "quantity": 1}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:tallgrass", "quantity": 1, "functions": [{"function": "set_data", "data": 2}]}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:reeds", "quantity": 1}]}, {"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pumpkin", "quantity": 1}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:kelp", "quantity": 1}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:cactus", "quantity": 1}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pumpkin_seeds", "quantity": 1}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:melon_seeds", "quantity": 1}]}, {"max_uses": 8, "weight": 6, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:sapling", "functions": [{"function": "random_block_state", "block_state": "sapling_type", "values": {"min": 0, "max": 5}}], "quantity": 1}]}, {"max_uses": 12, "weight": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:dye", "quantity": 3, "functions": [{"function": "random_aux_value", "values": {"min": 0, "max": 15}}]}]}, {"max_uses": 8, "weight": 5, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:coral_block", "quantity": 1, "functions": [{"function": "random_block_state", "block_state": "coral_color", "values": {"min": 0, "max": 4}}]}]}, {"max_uses": 12, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:vine", "quantity": 1}]}, {"max_uses": 12, "weight": 2, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:brown_mushroom", "quantity": 1}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:waterlily", "quantity": 2}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:small_dripleaf_block", "quantity": 2}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:sand", "quantity": 8, "functions": [{"function": "set_data", "data": 0}]}]}, {"max_uses": 6, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:sand", "quantity": 4, "functions": [{"function": "set_data", "data": 1}]}]}, {"max_uses": 5, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:pointed_dripstone", "quantity": 2}]}]}, {"num_to_select": 4, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 8}], "gives": [{"item": "minecraft:crossbow", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 10}], "gives": [{"item": "minecraft:trident", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 25}], "gives": [{"item": "minecraft:bow", "quantity": 1, "functions": [{"function": "specific_enchants", "enchants": [{"id": "infinity", "level": 1}, {"id": "flame", "level": 1}]}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 7}], "gives": [{"item": "minecraft:bow", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}]}, {"num_to_select": 12, "trades": [{"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:iron_pickaxe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:iron_sword", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:iron_axe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:iron_shovel", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:iron_hoe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:iron_helmet", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:iron_chestplate", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:iron_leggings", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "minecraft:iron_boots", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:diamond_pickaxe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 6}], "gives": [{"item": "minecraft:diamond_sword", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:diamond_axe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:diamond_shovel", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:diamond_hoe", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:diamond_helmet", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:diamond_chestplate", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:diamond_leggings", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}, {"wants": [{"item": "minecraft:emerald", "quantity": 4}], "gives": [{"item": "minecraft:diamond_boots", "quantity": 1, "functions": [{"function": "enchant_randomly", "treasure": false}]}], "max_uses": 3}]}]}]}