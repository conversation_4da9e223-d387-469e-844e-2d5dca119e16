import { EntityComponentTypes, EntityTypeFamilyComponent, system, world } from "@minecraft/server";
import { activateInactiveElectronReactorCore } from "../../blocks/electronReactorCore";
import { stormWeavers, cleanupStormWeaver, restoreChainedZombies } from "../../turrets/stormWeaver";
import { pulsarSystems } from "../../turrets/pulsarSystem";
import { repairArrayCooldown } from "../../turrets/repairArray";
import { meleeWeaponCooldown } from "../../weapons/melee";
import { setEntityToCardinalDirection } from "./entityCardinalFacing";
import {
  handleVanillaMobTransformation,
  handleEntityRemoveCleanup,
  handleEntityHurtEvent,
  handleEntityHitEntityEvent,
  handleEntityTriggerEvents
} from "./entities";

// Initialize entity event handlers
export function initializeEntityEvents() {
  // Entity Spawn Event
  world.afterEvents.entitySpawn.subscribe((data) => {
    const entity = data.entity;
    const entityType = entity.typeId;
    const entityId = entity.id;

    if (entityType === "minecraft:lightning_bolt") {
      const blockHit = entity.dimension.getBlock(entity.location);
      if (blockHit?.permutation?.matches("minecraft:lightning_rod")) {
        let run = system.run(() => {
          activateInactiveElectronReactorCore(blockHit);
          system.clearRun(run);
        });
      }
    }

    if (entityType === "rza:storm_weaver") {
      stormWeavers["rza:chain_length"].set(entityId, 10);
    }

    if (entityType === "rza:pulsar_system") {
      pulsarSystems["rza:cooldown"].set(entityId, 600);
      pulsarSystems["rza:fire_time"].set(entityId, 0);
      pulsarSystems["rza:pulse_radius_offset"].set(entityId, 0);
    }

    if (entityType === "rza:repair_array") {
      repairArrayCooldown.set(entityId, Math.floor(Math.random() * (40 - 0 + 1)) + 0);
    }

    if (entityType == "minecraft:pillager" || entityType == "minecraft:vindicator") {
      if (!meleeWeaponCooldown.has(entityId)) meleeWeaponCooldown.set(entityId, 0);
    }

    if (entityType === "rza:witherator") setEntityToCardinalDirection(entity);

    // Transform vanilla mobs into zombies
    handleVanillaMobTransformation(entity);
    return;
  });

  // Entity Load Event
  world.afterEvents.entityLoad.subscribe((data) => {
    const entity = data.entity;
    const entityType = entity.typeId;
    const entityId = entity.id;

    if (entityType === "rza:storm_weaver") {
      stormWeavers["rza:chain_length"].set(entityId, 10);
      let run = system.runTimeout(() => {
        restoreChainedZombies(entity);
        system.clearRun(run);
      }, 80);
    }

    if (entityType === "rza:pulsar_system") {
      pulsarSystems["rza:cooldown"].set(entityId, Math.floor(Math.random() * (600 - 100 + 1)) + 100);
      pulsarSystems["rza:fire_time"].set(entityId, 0);
      pulsarSystems["rza:pulse_radius_offset"].set(entityId, 0);
    }

    if (entityType === "rza:repair_array") {
      repairArrayCooldown.set(entityId, Math.floor(Math.random() * (40 - 0 + 1)) + 0);
    }

    if (entityType == "minecraft:pillager" || entityType == "minecraft:vindicator") {
      if (!meleeWeaponCooldown.has(entityId)) meleeWeaponCooldown.set(entityId, 0);
    }
    return;
  });

  // Entity Remove Events
  world.beforeEvents.entityRemove.subscribe((data) => {
    const removedEntity = data.removedEntity;
    if (removedEntity.typeId === "rza:storm_weaver" && !stormWeavers["rza:destroyed_weavers"].has(removedEntity.id)) {
      let run = system.run(() => {
        cleanupStormWeaver(removedEntity, true);
        system.clearRun(run);
      });
    }
    return;
  });

  world.afterEvents.entityRemove.subscribe((data) => {
    const entityType = data.typeId;
    const entityId = data.removedEntityId;

    handleEntityRemoveCleanup(entityType, entityId);
    return;
  });

  // Entity Die Event
  world.afterEvents.entityDie.subscribe((data) => {
    const entity = data.deadEntity;
    if (entity?.typeId === "rza:storm_weaver") {
      let run = system.run(() => {
        cleanupStormWeaver(entity, false);
        stormWeavers["rza:destroyed_weavers"].add(entity.id);
        system.clearRun(run);
      });
    }
    return;
  });

  // Entity Hurt Events
  world.afterEvents.entityHurt.subscribe((data) => {
    const entity = data.hurtEntity;
    const source = data.damageSource.damagingEntity;
    const sourceId = source?.typeId;
    const damage = data.damage;
    const isZombie =
      entity.hasComponent(EntityComponentTypes.TypeFamily) &&
      (entity.getComponent(EntityComponentTypes.TypeFamily) as EntityTypeFamilyComponent).hasTypeFamily("zombie");

    handleEntityHurtEvent(entity, source, sourceId, damage, isZombie);
    return;
  });

  world.afterEvents.entityHitEntity.subscribe((data) => {
    handleEntityHitEntityEvent(data);
    return;
  });

  // Entity Trigger Events
  world.afterEvents.dataDrivenEntityTrigger.subscribe(handleEntityTriggerEvents, {
    eventTypes: ["rza:configure", "rza:leap", "rza:sonic_charge", "rza:laser_pulse", "rza:lightning_strike", "rza:explode", "rza:alpha_zombie_buff"]
  });
  return;
}
