{"format_version": "1.10.0", "animations": {"animation.ravager.idle": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.6", "bones": {"body": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mouth": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "horns": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg3": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg2": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.ravager.walk": {"loop": true, "animation_length": 1.5, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*3))", "bones": {"neck": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "horns": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"rotation": {"0.0": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}}}, "leg2": {"rotation": {"0.0": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}, "0.875": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}}}, "leg3": {"rotation": {"0.0": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}, "0.375": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.5": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}, "0.125": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "0.375": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.375": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "1.5": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}}}}}, "animation.ravager.run": {"loop": true, "animation_length": 1.25, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2.5))", "bones": {"neck": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, 1, 1], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}}}, "horns": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"rotation": {"0.0": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [-5, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [-4.02, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.4583": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "0.7917": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [-4.02, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 4.62, 0.29], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.4583": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "0.7917": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "1.2083": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 4.62, 0.29], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "1.0417": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}}}, "leg2": {"rotation": {"0.0": {"post": [20.87, 0, 0], "lerp_mode": "catmullrom"}, "0.0833": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}, "0.4583": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.7917": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "1.0833": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [20.87, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -1.28, 0.87], "lerp_mode": "catmullrom"}, "0.0833": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "0.4583": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.7917": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "1.0833": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, -1.28, 0.87], "lerp_mode": "catmullrom"}}}, "leg3": {"rotation": {"0.0": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": [-20, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [-10, 0, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [17.5, 0, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.25": {"post": [12.05, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}, "0.0833": {"post": [0, 5, 0.5], "lerp_mode": "catmullrom"}, "0.2917": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, -0.25, -4.25], "lerp_mode": "catmullrom"}, "0.9167": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "1.125": {"post": [0, -1, 1], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 2.38, 0.91], "lerp_mode": "catmullrom"}}}}}, "animation.ravager.attack": {"loop": "hold_on_last_frame", "animation_length": 0.75, "bones": {"neck": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, -9], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mouth": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.ravager.stunned": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.6", "bones": {"head": {"rotation": {"0.0": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [12.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [12.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "mouth": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "horns": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "body": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -0.5, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg2": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "leg3": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0.5, 0.25, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.ravager.roar": {"loop": "hold_on_last_frame", "animation_length": 2.125, "bones": {"neck": {"position": {"0.0": [0, 0, 0], "0.25": [0, 10, 0], "0.5": [0, -4, 0], "1.5": [0, -4, 0], "1.75": [0, 10, 0], "2.0": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [15, 0, 0], "0.25": [5, 0, 0], "0.375": [-10, 0, 0], "0.5": [-2.5, 0, 0], "0.625": [-2.4905, -0.21782, -4.99527], "0.75": [-2.4905, 0.21782, 4.99527], "0.875": [-2.4905, -0.21782, -4.99527], "1.0": [-2.4905, 0.21782, 4.99527], "1.125": [-2.4905, -0.21782, -4.99527], "1.25": [-2.4905, 0.21782, 4.99527], "1.375": [-2.4905, -0.21782, -4.99527], "1.5": [-2.4905, -0.21782, -4.99527], "1.75": [0, 0, 0]}}, "mouth": {"rotation": {"0.375": [0, 0, 0], "0.5833": [45, 0, 0], "1.375": [45, 0, 0], "1.625": [0, 0, 0]}}, "nose": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-15, 0, 0], "0.25": [0, 0, 0], "0.4167": [-10, 0, 0], "0.5": [-10, 0, 0], "1.125": [-10, 0, 0], "1.375": [0, 0, 0]}}, "body": {"rotation": {"0.0": [0, 0, 0], "0.25": [-17.5, 0, 0], "0.5": [12.5, 0, 0], "0.625": [12.45392, -1.08089, 4.88206], "0.75": [12.45392, 1.08089, -4.88206], "0.875": [12.45392, -1.08089, 4.88206], "1.0": [12.45392, 1.08089, -4.88206], "1.125": [12.45392, -1.08089, 4.88206], "1.25": [12.45392, 1.08089, -4.88206], "1.375": [12.5, 0, 0], "1.5": [12.5, 0, 0], "1.75": [-17.5, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [0, 8, 0], "0.5": [0, -6, 0], "1.5": [0, -6, 0], "1.75": [0, 8, 0], "2.0": [0, 0, 0]}}, "leg0": {"rotation": {"0.375": [0, 0, 0], "0.5833": [7.5, 0, 0], "0.75": [15.9477, -7.30111, 23.97594], "1.375": [15.9477, -7.30111, 23.97594], "1.5417": [7.5, 0, 0], "1.75": [0, 0, 0]}, "position": {"0.375": [0, 0, 0], "0.5833": [0, 5, 1], "0.75": [0, -5, 1], "1.375": [0, -5, 1], "1.5417": [0, 5, 1], "1.75": [0, 0, 0]}}, "leg1": {"rotation": {"0.0": [0, 0, 0], "0.2083": [7.5, 0, 0], "0.375": [15.9477, 7.30111, -23.97594], "1.75": [15.9477, 7.30111, -23.97594], "1.9167": [7.5, 0, 0], "2.125": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.2083": [0, 5, 1], "0.375": [0, -5, 1], "1.75": [0, -5, 1], "1.9167": [0, 5, 1], "2.125": [0, 0, 0]}}, "leg2": {"rotation": {"0.0": [0, 0, 0], "0.125": [15, 0, 0], "0.25": [15, 0, 0], "0.375": [-36.63788, 21.48944, 17.58347], "0.5": [-36.63788, 21.48944, 17.58347], "0.625": [-36.63788, 21.48944, 17.58347], "0.75": [-36.63788, 21.48944, 17.58347], "0.875": [-36.63788, 21.48944, 17.58347], "1.0": [-36.63788, 21.48944, 17.58347], "1.125": [-36.63788, 21.48944, 17.58347], "1.25": [-36.63788, 21.48944, 17.58347], "1.375": [-36.63788, 21.48944, 17.58347], "1.5": [-36.63788, 21.48944, 17.58347], "1.625": [-36.63788, 21.48944, 17.58347], "1.75": [-2.5, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [0, 8, 0], "0.5": [0, -7, 0], "0.625": [0, -7, 0], "0.75": [0, -7.5, 0], "0.875": [0, -7, 0], "1.0": [0, -7.5, 0], "1.125": [0, -7, 0], "1.25": [0, -7.5, 0], "1.375": [0, -7.5, 0], "1.5": [0, -7, 0], "1.75": [0, 8, 0], "2.0": [0, 0, 0]}}, "leg3": {"rotation": {"0.0": [0, 0, 0], "0.125": [15, 0, 0], "0.25": [15, 0, 0], "0.375": [-36.63788, -21.48944, -17.58347], "0.5": [-36.63788, -21.48944, -17.58347], "0.625": [-36.63788, -21.48944, -17.58347], "0.75": [-36.63788, -21.48944, -17.58347], "0.875": [-36.63788, -21.48944, -17.58347], "1.0": [-36.63788, -21.48944, -17.58347], "1.125": [-36.63788, -21.48944, -17.58347], "1.25": [-36.63788, -21.48944, -17.58347], "1.375": [-36.63788, -21.48944, -17.58347], "1.5": [-36.63788, -21.48944, -17.58347], "1.625": [-36.63788, -21.48944, -17.58347], "1.75": [-2.5, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.25": [0, 8, 0], "0.5": [0, -7, 0], "0.625": [0, -7.5, 0], "0.75": [0, -7, 0], "0.875": [0, -7.5, 0], "1.0": [0, -7, 0], "1.125": [0, -7.5, 0], "1.25": [0, -7, 0], "1.375": [0, -7.5, 0], "1.5": [0, -7, 0], "1.75": [0, 8, 0], "2.0": [0, 0, 0]}}}}, "animation.ravager.death": {"loop": "hold_on_last_frame", "animation_length": 2.25, "anim_time_update": "q.anim_time+q.delta_time*2.4", "bones": {"root": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.14003, 0], "0.0833": [0, -0.31744, 0], "0.125": [0, -0.53393, 0], "0.1667": [0, -0.79063, 0], "0.2083": [0, -1.08871, 0], "0.25": [0, -1.42952, 0], "0.2917": [0, -1.8148, 0], "0.3333": [0, -2.24565, 0], "0.375": [0, -2.7239, 0], "0.4167": [0, -3.25094, 0], "0.4583": [0, -3.82842, 0], "0.5": [0, -4.45805, 0], "0.5417": [0, -5.14126, 0], "0.5833": [0, -5.8797, 0], "0.625": [0, -6.67434, 0], "0.6667": [0, -7.5259, 0], "0.7083": [0, -8.43423, 0], "0.75": [0, -9.39749, 0], "0.7917": [0, -10.41117, 0], "0.8333": [0, -11.46543, 0], "0.875": [0, -12.5399, 0], "0.9167": [0, -13.59094, 0], "0.9583": [0, -14.51491, 0], "1.0": [0, -15, 0], "1.0417": [0, -14.98503, 0], "1.0833": [0, -14.94039, 0], "1.1667": [0, -14.80961, 0], "1.2083": [0, -14.76497, 0], "1.25": [0, -14.75, 0], "1.5": [0, -15, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "1.0": [-12.5, 0, 0], "1.4583": [0, 0, 0], "1.75": [-2.5, 0, 0], "2.0833": [0, 0, 0]}}, "horns": {"rotation": {"1.2083": [0, 0, 0], "1.5417": [5, 0, 0], "1.8333": [0, 0, 0], "2.25": [12.5, 0, 0]}}, "nose": {"rotation": {"0.0": [0, 0, 0], "0.5": [-15, 0, 0], "1.2083": [-2.5, 0, 0], "1.4583": [0, 0, 0], "1.8333": [-15, 0, 0], "2.1667": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, 75], "1.25": [0, 0, 72.5], "1.5": [0, 0, 75]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -8, 0], "1.25": [0, -7.75, 0], "1.5": [0, -8, 0]}}, "leg1": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, -75], "1.25": [0, 0, -72.5], "1.5": [0, 0, -75]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -8, 0], "1.25": [0, -7.75, 0], "1.5": [0, -8, 0]}}, "leg2": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, 75], "1.25": [0, 0, 72.5], "1.5": [0, 0, 75]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -8, 0], "1.25": [0, -7.75, 0], "1.5": [0, -8, 0]}}, "leg3": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, -75], "1.25": [0, 0, -72.5], "1.5": [0, 0, -75]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -8, 0], "1.25": [0, -7.75, 0], "1.5": [0, -8, 0]}}}}}}