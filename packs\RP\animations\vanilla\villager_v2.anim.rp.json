{"format_version": "1.10.0", "animations": {"animation.villager.baby_transform": {"loop": true, "bones": {"head": {"scale": [1.5, 1.5, 1.5]}}}, "animation.villager.idle": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+q.delta_time*0.4", "bones": {"arms": {"rotation": {"0.0": {"post": [-42.97, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-50.47, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-42.97, 0, 0], "lerp_mode": "catmullrom"}}, "position": [0, 0, -1]}, "head": {"rotation": {"0.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}}}, "nose": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "root": {"rotation": {"0.0": [2.5, 0, 0], "0.5": [0, 0, 0], "1.0": [2.5, 0, 0]}}}}, "animation.villager.walk": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"leg0": {"rotation": {"0.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [10, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [5, 0, 0], "lerp_mode": "catmullrom"}}}, "root": {"position": {"0.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-47.97, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-72.97, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-47.97, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-72.97, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [19, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [15, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [20, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [19, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.villager.run": {"loop": true, "animation_length": 1, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2.5))", "bones": {"root": {"position": {"0.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -2, 0], "lerp_mode": "catmullrom"}, "0.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0.31, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -3, 0], "lerp_mode": "catmullrom"}}}, "spine": {"rotation": {"0.0": {"post": [-21, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-25, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-13.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-25, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-13.5, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-21, 0, 0], "lerp_mode": "catmullrom"}}}, "head": {"rotation": {"0.0": {"post": [-6, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-6, 0, 0], "lerp_mode": "catmullrom"}}}, "arms": {"rotation": {"0.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}, "0.1667": {"post": [-55.47, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [-85, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-55.47, 0, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [-85, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-62.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, -1, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, -0.5, -1], "lerp_mode": "catmullrom"}}}, "leg0": {"rotation": {"0.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 1], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, 0.5], "lerp_mode": "catmullrom"}}}, "leg1": {"rotation": {"0.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [45, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [-45, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5833": {"post": [0, 1, 0], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 1, -2], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0.5, -1.5], "lerp_mode": "catmullrom"}}}}}, "animation.villager.throw": {"loop": "hold_on_last_frame", "animation_length": 0.70833, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-12.5, 0, 0], "0.2083": [10, 0, 0], "0.5417": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [5, 0, 0], "0.2083": [-7.5, 0, 0], "0.2917": [0, 0, 0]}}, "arms": {"rotation": {"0.0": [0, 0, 0], "0.125": [-60, 0, 0], "0.3333": [0, 0, 0]}}}}, "animation.villager.get_in_bed": {"loop": true, "animation_length": 0.58333, "bones": {"root": {"rotation": [-90, 0, 0], "position": [0, 1, -15]}, "arms": {"rotation": [-30.47, 0, 0], "position": [0, 0, -1]}}}, "animation.villager.raise_arms": {"loop": true, "bones": {"arms": {"rotation": ["variable.raise_arms*-25", 0, 0]}}}, "animation.villager.death": {"loop": "hold_on_last_frame", "animation_length": 1, "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-25, 0, 0], "0.5": [-90, 0, 0], "0.5833": [-87.5, 0, 0], "0.6667": [-90, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [22.5, 0, 0], "0.4167": [17.5, 0, 0], "0.5": [22.5, 0, 0], "0.5417": [4.69, 0, 0], "0.5833": [22.5, 0, 0], "0.9167": [0, 0, 0]}}, "nose": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-17.5, 0, 0], "0.4583": [-17.5, 0, 0], "0.5": [0, 0, 0], "0.5417": [-17.5, 0, 0], "0.5833": [0, 0, 0]}}, "arms": {"rotation": {"0.0": [-57.5, 0, 0], "0.1667": [-77.5, 0, 0], "0.5": [-82.5, 0, 0], "0.5417": [-67.19, 0, 0], "0.5833": [-82.5, 0, 0], "0.8333": [-40, 0, 0]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.125": [-67.5, 0, 0], "0.5": [-10, 0, 0], "0.5417": [0, 0, 0], "0.5833": [-20, 0, 0], "0.75": [0, 0, 0]}}}}}}