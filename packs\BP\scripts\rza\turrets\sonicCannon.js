import { EntityDamageCause } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";
import { calculateDirection } from "../utils/vector3";
export function sonicCannonHit(entity, source) {
    const cannonDir = source.getViewDirection();
    entity.applyImpulse({
        x: cannonDir.x * 3,
        y: cannonDir.y * 8,
        z: cannonDir.z * 3
    });
    return;
}
export function fireSonicCharge(entity) {
    const dimension = entity.dimension;
    const location = entity.location;
    const target = entity.target;
    if (!target)
        return;
    const targetLocation = target.location;
    const direction = calculateDirection(location, targetLocation);
    const startOffset = 1.5;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.55 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };
    const positions = fixedLenRaycast(startPos, direction, 48, 2);
    for (const pos of positions) {
        try {
            dimension.spawnParticle("rza:sonic_charge", pos);
            dimension.getEntities({ location: pos, families: ["zombie"], maxDistance: 5 }).forEach((zombie) => {
                zombie.applyDamage(10, { cause: EntityDamageCause.entityAttack, damagingEntity: entity });
            });
        }
        catch (e) { }
    }
    return;
}
